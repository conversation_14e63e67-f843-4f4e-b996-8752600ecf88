"use client"

import { Pencil } from "lucide-react";
import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { Header } from "../components/header";
import { Avatar, AvatarFallback, AvatarImage } from "../components/ui/avatar";
import { Footer } from "../components/footer";
import { Badge } from "../components/ui/badge";
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card";
import { useRouter } from "next/navigation";
import Loading from "../components/ui/loading";

interface ExtendedUser {
    id?: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    role?: string | null;
    accessLevel?: string | null;
}

export default function ProfilePage() {
    const { data: session, status, update } = useSession();
    const [isLoading, setIsLoading] = useState(true);
    const [isEditing, setIsEditing] = useState(false);
    const [newName, setNewName] = useState<string>('');
    const [displayName, setDisplayName] = useState<string>('');
    const router = useRouter();

    const user = session?.user as ExtendedUser;

    useEffect(() => {
        if (status === "authenticated") {
            const fetchUserRole = async () => {
                setIsLoading(true);
                try {
                    if (session?.user?.email) {
                        const email = session.user.email;

                        const response = await fetch(`/api/users/${email}`);
                        if (response.ok) {
                            const user = await response.json();
                            setNewName(user?.name ?? '');
                            setDisplayName(user?.name ?? '');
                        } else {
                            console.error(`Failed to fetch user role. Status: ${response.status}`);
                            const errorResponse = await response.json();
                            console.error("Error response:", errorResponse);
                        }
                    } else {
                        console.error("No email found in session.");
                    }
                } catch (error) {
                    console.error("Error fetching user role:", error);
                } finally {
                    setIsLoading(false);
                }
            };

            fetchUserRole();
        } else if (status === 'unauthenticated') {
            router.push('/');
        }
    }, [status, session, router]);

    const formatUserRole = (role: string) => {
        switch (role) {
            case "ADMIN":
                return "Administrador";
            case "VIEWER":
                return "Usuário";
            case "DEVELOPER":
                return "Desenvolvedor";
            case "COPY":
                return "Copy";
            case "DESIGNER":
                return "Designer";
            default:
                return "Desconhecido";
        }
    };

    const handleSaveChanges = async () => {
        try {
            const updatedUser = { name: newName };
            const response = await fetch(`/api/users/${user?.email}`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(updatedUser),
            });

            if (response.ok) {
                setIsEditing(false);
                toast.success("Alterações salvas com sucesso.");
                
                // Atualizar o nome local para exibição imediata
                setDisplayName(newName);

                // Atualizar a sessão com o novo nome
                await update({ 
                    ...session, 
                    user: { 
                        ...session?.user, 
                        name: newName 
                    } 
                });
                
                // Forçar uma atualização completa da página
                window.location.reload();
            } else {
                console.error("Failed to update user data.");
                toast.error("Erro ao salvar alterações.");
            }
        } catch (error) {
            console.error("Error saving changes:", error);
            toast.error("Erro ao salvar alterações.");
        }
    };

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:p-8 flex-grow">
                {isLoading ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : (
                    <div className="border-b pb-16">
                        <h2 className="text-lg font-semibold mb-7 border-b border-zinc-200 dark:border-zinc-800">Perfil</h2>
                        <div className="mb-8 flex gap-2 items-center">
                            <Avatar className="w-16 h-16 xs:w-24 xs:h-24 border-2 border-zinc-200">
                                <AvatarImage src={user?.image ?? "https://github.com/shadcn.png"} alt={displayName || user?.name || "Usuário"} />
                                <AvatarFallback>{(displayName || user?.name) ? String(displayName || user?.name)[0] : "U"}</AvatarFallback>
                            </Avatar>
                            <div>
                                <p className="font-semibold">{displayName || user?.name}</p>
                                <p className="mb-1 text-xs xs:text-sm text-zinc-500">{user?.email}</p>
                                <Badge className="font-mono font-normal">
                                    {formatUserRole(user?.role ?? "Usuário")}
                                </Badge>
                            </div>
                        </div>

                        <Button variant="outline" onClick={() => setIsEditing(true)}>
                            <Pencil /> Editar
                        </Button>

                        {isEditing && (
                            <Card className="mt-6">
                                <CardHeader>
                                    <CardTitle>
                                        <h3 className="font-semibold">Editar dados</h3>
                                    </CardTitle>
                                    <CardDescription>
                                        No momento, você pode editar apenas o seu nome. Para alterar sua função, caso você seja um administrador, vá até a página de usuários, basta clicar em <Badge variant="outline" className="text-zinc-600">Painel</Badge> no menu lateral.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="mb-4">
                                        <label className="block mb-2 text-sm" htmlFor="name">
                                            Nome
                                        </label>
                                        <Input
                                            type="text"
                                            id="name"
                                            value={newName}
                                            onChange={(e) => setNewName(e.target.value)}
                                            className="text-sm w-full p-2 border border-zinc-300 rounded-md"
                                        />
                                    </div>
                                    <Button onClick={handleSaveChanges}>Salvar alterações</Button>
                                    <Button variant="outline" onClick={() => setIsEditing(false)} className="ml-4">
                                        Cancelar
                                    </Button>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                )}
            </div>
            <Footer />
        </div >
    );
};
