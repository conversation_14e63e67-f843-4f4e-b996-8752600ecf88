"use client"

import { <PERSON><PERSON> } from "@/app/components/footer";
import { Header } from "@/app/components/header";
import { NotAllowed } from "@/app/components/not-allowed";
import { Badge } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/app/components/ui/dialog";
import Loading from "@/app/components/ui/loading";
import { Separator } from "@/app/components/ui/separator";
import { Client } from "@prisma/client";
import { AppWindowMac, Grid3X3, InfoIcon, MoveLeft } from "lucide-react";
import { useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { FeedStructuringContent } from "@/app/components/feed-structuring-content";
import { ClientNavigationModal } from "@/app/components/client-navigation-modal";

export default function FeedStructuringPage() {
    const { data: session, status } = useSession();
    const [client, setClient] = useState<Client | null>(null);
    const [isAdmin, setIsAdmin] = useState(false);
    const [isFetchingRole, setIsFetchingRole] = useState(true);
    const [clientNavigationOpen, setClientNavigationOpen] = useState(false);
    const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
    const [viewedClients, setViewedClients] = useState<{ id: string; name: string }[]>([]);
    const router = useRouter();
    const params = useParams();
    const id = params?.id as string;

    const handleOpenClientNavigation = (clientId: string) => {
        setSelectedClientId(clientId);
        setClientNavigationOpen(true);
    };

    useEffect(() => {
        const recentClients = JSON.parse(localStorage.getItem('viewedClients') || '[]');
        setViewedClients(recentClients);
    }, []);

    useEffect(() => {
        if (status === "authenticated") {
            if (id) {
                fetch(`/api/clients/${id}?include=resultsReport.results`)
                    .then(res => res.json())
                    .then(data => {
                        setClient(data);
                    })
                    .catch(error => console.error("Erro ao buscar dados do cliente:", error))
                    .finally(() => setIsFetchingRole(false));
            }

            const fetchUserRole = async () => {
                try {
                    if (session?.user?.email) {
                        const response = await fetch(`/api/users/${session.user.email}`);
                        if (response.ok) {
                            const user = await response.json();

                            const hasPermission =
                                user?.role === "ADMIN" ||
                                user?.role === "DEVELOPER" ||
                                user?.role === "DESIGNER"

                            setIsAdmin(hasPermission);
                        } else {
                            console.error("Falha ao buscar dados do usuário:", response.status);
                            setIsAdmin(false);
                        }
                    } else {
                        setIsAdmin(false);
                    }
                } catch (error) {
                    console.error("Erro ao buscar função do usuário:", error);
                    setIsAdmin(false);
                } finally {
                    setIsFetchingRole(false);
                }
            };

            fetchUserRole();
        } else if (status === 'unauthenticated') {
            router.push('/');
        }
    }, [status, session, router, id]);

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:px-8 xs:pt-4 xs:pb-8 flex-grow">
                {isFetchingRole || !client ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : isAdmin ? (
                    <>
                        <div className="flex justify-between items-center">
                            <div className="flex items-start xs:items-center justify-between w-full gap-2">
                                <div className="flex items-center gap-2">
                                    {!isAdmin ? (
                                        <Button
                                            variant="outline"
                                            onClick={() => router.push('/clients')}
                                        >
                                            <MoveLeft className="h-4 w-4" />
                                        </Button>
                                    ) : (
                                        <Button
                                            variant="outline"
                                            size="icon"
                                            onClick={() => router.push('/')}
                                        >
                                            <MoveLeft className="h-4 w-4" />
                                        </Button>
                                    )}
                                    <Button
                                        variant="outline"
                                        size="icon"
                                        onClick={() => handleOpenClientNavigation(client.id)}
                                    >
                                        <AppWindowMac className="h-4 w-4" />
                                    </Button>
                                </div>
                                <ClientNavigationModal
                                    open={clientNavigationOpen}
                                    onOpenChange={setClientNavigationOpen}
                                    clientId={selectedClientId}
                                    clientName={viewedClients.find(client => client.id === selectedClientId)?.name}
                                />
                                <div className="flex flex-col xs:flex-row items-end xs:items-center gap-2 group">
                                    <h1 className="text-sm xs:text-lg uppercase font-geistMono font-semibold tracking-tight">
                                        Estruturação do Feed
                                    </h1>
                                    <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                                        <Grid3X3 size={24} color="#db5743" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h2 className="mt-4 text-lg font-semibold">{client.name}</h2>
                            <Link
                                href={`https://www.instagram.com/${client.instagramUsername}`}
                                target="_blank"
                                className="text-sm text-blue-500 hover:underline underline-offset-4 cursor-pointer inline-flex items-center gap-1"
                            >
                                <Image src="/instagram.svg" width={14} height={14} alt="Instagram" />
                                {client.instagramUsername}
                            </Link>
                            <div className="border-t pt-1 border-zinc-200 dark:border-zinc-800 mt-2">
                                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-1 mb-2">
                                    <h3 className="flex items-center gap-1 text-sm font-semibold">
                                        <Grid3X3 size={16} />
                                        Estruturação do feed
                                    </h3>
                                    <div className="flex items-center gap-1">
                                        <Badge variant="secondary">
                                            etapa 3
                                        </Badge>
                                        <Badge variant="secondary">
                                            design visual
                                        </Badge>
                                        <Dialog>
                                            <DialogTrigger asChild>
                                                <Button variant="link" size="icon">
                                                    <InfoIcon />
                                                </Button>
                                            </DialogTrigger>
                                            <DialogContent
                                                className="h-[85vh] p-0 overflow-y-auto"
                                                style={{ maxWidth: "550px !important", width: "550px !important" }}
                                            >
                                                <div className="p-6">
                                                    <DialogHeader className="mb-4">
                                                        <DialogTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                                                            Saiba mais
                                                        </DialogTitle>
                                                        <DialogDescription className="font-medium text-gray-700 dark:text-gray-200">
                                                            Etapa 3 — Estruturação do feed e design visual
                                                        </DialogDescription>
                                                    </DialogHeader>
                                                    <div>
                                                        <p className="mb-2">Objetivo:</p>
                                                        <p className="text-zinc-700 dark:text-zinc-400">Estabelecer uma identidade visual coesa e atrativa para o perfil social do cliente.</p>
                                                    </div>
                                                    <Separator
                                                        className="my-2"
                                                    />
                                                    <div>
                                                        <p className="mb-2">Proposta de layout do feed:</p>
                                                        <ul className="list-disc pl-5 space-y-1 text-sm">
                                                            <li>Criar uma prévia visual do feed, considerando uma estética alinhada com a identidade da marca e a experiência do usuário.</li>
                                                            <li>Explorar diferentes tipos de grids e combinações de cores que reflitam a marca e mantenham a harmonia visual.</li>
                                                        </ul>
                                                    </div>
                                                    <Separator
                                                        className="my-2"
                                                    />
                                                    <div>
                                                        <p className="mb-2">Definição de paleta de cores, tipografia e elementos visuais:</p>
                                                        <p className="text-sm">Detalhar as escolhas visuais (paleta de cores, fontes, ícones) para que cada peça tenha um visual coerente e identifique claramente a marca.</p>
                                                    </div>
                                                </div>
                                            </DialogContent>
                                        </Dialog>
                                    </div>
                                </div>
                                <FeedStructuringContent clientId={id} client={client} />
                            </div>
                        </div>
                    </>
                ) : (
                    <NotAllowed
                        page="clients"
                    />
                )}
            </div>
            <Footer />
        </div>
    );
}