import { NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { Prisma } from "@prisma/client";

export async function GET(request: Request) {
    try {
        const { searchParams } = new URL(request.url);
        const unreadOnly = searchParams.get('unread') === 'true';

        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json([], { status: 200 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email || "" },
            select: { role: true },
        });

        const isAdmin = user?.role === "ADMIN" || user?.role === "DEVELOPER";

        const query: Prisma.NotificationFindManyArgs = {
            where: {
                OR: [
                    { userId: session.user.email || undefined },
                    {
                        userId: null,
                        ...(!isAdmin ? {
                            NOT: {
                                type: {
                                    in: ["new_feedback", "url_added"]
                                }
                            }
                        } : {})
                    }
                ],
                NOT: {
                    type: "status_change"
                },
                ...(unreadOnly ? { isRead: false } : {})
            },
            orderBy: {
                createdAt: 'desc'
            },
            take: unreadOnly ? 20 : 100
        };

        const notifications = await prisma.notification.findMany(query);

        return NextResponse.json(notifications, { status: 200 });
    } catch (error) {
        console.error("Erro ao buscar notificações:", error);
        return NextResponse.json(
            { error: "Erro interno do servidor" },
            { status: 500 }
        );
    }
}

export async function PATCH(request: Request) {
    try {
        const { id, isRead } = await request.json();

        if (!id) {
            return NextResponse.json(
                { error: "ID da notificação é obrigatório" },
                { status: 400 }
            );
        }

        const notification = await prisma.notification.update({
            where: { id },
            data: { isRead: isRead ?? true }
        });

        return NextResponse.json(notification, { status: 200 });
    } catch (error) {
        console.error("Erro ao atualizar notificação:", error);
        return NextResponse.json(
            { error: "Erro interno do servidor" },
            { status: 500 }
        );
    }
}