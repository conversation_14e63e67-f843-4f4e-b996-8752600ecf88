import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { Prisma } from "@prisma/client";

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
    }

    const url = new URL(req.url);
    const clientId = url.searchParams.get('clientId');
    const userId = url.searchParams.get('userId');

    const query: Prisma.GeneralDemandFindManyArgs = {
      where: {},
      include: {
        client: {
          select: {
            id: true,
            name: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            role: true,
          },
        },
      },
      orderBy: {
        dueDate: 'asc',
      },
    };

    if (clientId) {
      query.where = {
        ...query.where,
        clientId: clientId
      };
    }

    if (userId) {
      query.where = {
        ...query.where,
        assignedToId: userId
      };
    }

    const generalDemands = await prisma.generalDemand.findMany(query);

    return NextResponse.json(generalDemands);
  } catch (error) {
    console.error("Erro ao buscar demandas gerais:", error);
    return NextResponse.json(
      { error: "Erro ao processar a solicitação" },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email || "" },
      select: { role: true, accessLevel: true },
    });

    if (
      user?.role !== "ADMIN" &&
      user?.role !== "DEVELOPER" &&
      user?.accessLevel !== "EDITOR"
    ) {
      return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
    }

    const data = await req.json();
    const {
      title,
      description,
      dueDate,
      status,
      priority,
      clientId,
      assignedToId,
      useLooseClient,
      looseClientName,
      urlStructuringFeed
    } = data;

    if (!title) {
      return NextResponse.json(
        { error: "Título é obrigatório" },
        { status: 400 }
      );
    }

    if (!useLooseClient && !clientId) {
      return NextResponse.json(
        { error: "ID do cliente é obrigatório para clientes fixos" },
        { status: 400 }
      );
    }

    if (useLooseClient && !looseClientName) {
      return NextResponse.json(
        { error: "Nome do cliente é obrigatório para clientes não fixos" },
        { status: 400 }
      );
    }

    if (!useLooseClient && clientId) {
      const clientExists = await prisma.client.findUnique({
        where: { id: clientId },
      });

      if (!clientExists) {
        return NextResponse.json(
          { error: "Cliente não encontrado" },
          { status: 404 }
        );
      }
    }

    if (assignedToId) {
      const assignedUser = await prisma.user.findUnique({
        where: { id: assignedToId },
      });

      if (!assignedUser) {
        return NextResponse.json(
          { error: "Usuário atribuído não encontrado" },
          { status: 404 }
        );
      }
    }

    const demandData: Prisma.GeneralDemandCreateInput = {
      title,
      description,
      dueDate: dueDate ? new Date(dueDate) : null,
      status,
      priority,
      urlStructuringFeed,
    };

    if (!useLooseClient && clientId) {
      demandData.client = { connect: { id: clientId } };
    } else if (useLooseClient && looseClientName) {
      const looseClient = await prisma.looseClient.create({
        data: {
          name: looseClientName
        }
      });
      demandData.looseClient = { connect: { id: looseClient.id } };
    }

    if (assignedToId) {
      demandData.assignedTo = { connect: { id: assignedToId } };
    }

    const newGeneralDemand = await prisma.generalDemand.create({
      data: demandData,
      include: {
        client: true,
        looseClient: true,
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    if (assignedToId) {
      const assignedUser = await prisma.user.findUnique({
        where: { id: assignedToId },
        select: { email: true },
      });

      const clientName = await (async () => {
        if (newGeneralDemand.clientId) {
          const client = await prisma.client.findUnique({
            where: { id: newGeneralDemand.clientId },
            select: { name: true }
          });
          return client ? client.name : "Cliente não especificado";
        } else if (newGeneralDemand.looseClientId) {
          const looseClient = await prisma.looseClient.findUnique({
            where: { id: newGeneralDemand.looseClientId },
            select: { name: true }
          });
          return looseClient ? looseClient.name : "Cliente não especificado";
        }
        return "Cliente não especificado";
      })();

      if (assignedUser) {
        await prisma.notification.create({
          data: {
            content: `Nova demanda pontual atribuída a você: ${title} (${clientName})`,
            type: "assigned_general_demand",
            entityId: newGeneralDemand.id,
            entityType: "general_demand",
            userId: assignedUser.email,
          },
        });
      }
    }

    return NextResponse.json(newGeneralDemand, { status: 201 });
  } catch (error) {
    console.error("Erro ao criar demanda geral:", error);
    return NextResponse.json(
      { error: "Erro ao processar a solicitação" },
      { status: 500 }
    );
  }
}
