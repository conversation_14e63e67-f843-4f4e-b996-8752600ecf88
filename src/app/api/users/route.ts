import { NextRequest, NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";
import { Role, AccessLevel } from "@prisma/client";

type UserUpdateData = {
    role?: Role;
    accessLevel?: AccessLevel;
};

export async function GET() {
    try {
        const users = await prisma.user.findMany({
            select: {
                id: true,
                name: true,
                image: true,
                email: true,
                role: true,
                accessLevel: true,
                archived: true
            },
        });

        return NextResponse.json(users);
    } catch (error) {
        console.error("Error fetching users:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}

export async function PATCH(req: NextRequest) {
    try {
        const data = await req.json();
        const { id, role, accessLevel } = data;

        if (!id) {
            return NextResponse.json(
                { error: "ID do usuário é obrigatório" },
                { status: 400 }
            );
        }

        const updateData: UserUpdateData = {};
        
        if (role !== undefined) {
            updateData.role = role as Role;
        }
        
        if (accessLevel !== undefined) {
            updateData.accessLevel = accessLevel as AccessLevel;
        }

        if (Object.keys(updateData).length === 0) {
            return NextResponse.json(
                { error: "Nenhum dado válido fornecido para atualização" },
                { status: 400 }
            );
        }

        const updatedUser = await prisma.user.update({
            where: { id },
            data: updateData,
        });

        return NextResponse.json(updatedUser, { status: 200 });
    } catch (error) {
        console.error("Erro ao atualizar usuário:", error);
        return NextResponse.json(
            { error: "Erro interno do servidor" },
            { status: 500 }
        );
    }
}
