import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
    try {
        const pendingContents = await prisma.content.findMany({
            where: {
                AND: [
                    {
                        status: "estruturação de feed"
                    },
                    {
                        OR: [
                            { destination: "Feed" },
                            { destination: "Story/Feed" }
                        ]
                    },
                    {
                        archived: false
                    }
                ]
            },
            include: {
                weeklyActivity: {
                    include: {
                        monthlyPlanning: {
                            include: {
                                client: {
                                    select: {
                                        id: true,
                                        name: true,
                                        archived: true
                                    }
                                }
                            }
                        }
                    }
                }
            },
            orderBy: {
                activityDate: 'asc'
            }
        });

        const filteredContents = pendingContents
            .filter(content => 
                content.weeklyActivity?.monthlyPlanning && 
                !content.weeklyActivity.monthlyPlanning.client.archived &&
                content.weeklyActivity.monthlyPlanning.status === "aprovado"
            )
            .map(content => ({
                id: content.id,
                contentType: content.contentType,
                status: content.status,
                destination: content.destination,
                details: content.details,
                activityDate: content.activityDate,
                clientId: content.weeklyActivity!.monthlyPlanning!.client.id,
                clientName: content.weeklyActivity!.monthlyPlanning!.client.name,
                week: content.weeklyActivity!.week,
                month: content.weeklyActivity!.monthlyPlanning!.month,
                year: content.weeklyActivity!.monthlyPlanning!.year
            }));

        return new NextResponse(JSON.stringify(filteredContents), {
            status: 200,
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'public, s-maxage=30, stale-while-revalidate=60',
            }
        });
    } catch (error) {
        console.error("Erro ao buscar conteúdos de estruturação de Feed:", error);
        return NextResponse.json({ message: "Erro ao buscar conteúdos" }, { status: 500 });
    }
}
