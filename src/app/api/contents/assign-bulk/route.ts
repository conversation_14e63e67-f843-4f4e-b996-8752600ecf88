import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

interface ClientGroup {
  name: string;
  count: number;
}

interface ClientGroups {
  [clientId: string]: ClientGroup;
}

export async function PATCH(req: Request) {
  try {
    const { contentIds, userId } = await req.json();

    if (!contentIds || !Array.isArray(contentIds) || contentIds.length === 0) {
      return NextResponse.json({ message: "Content IDs are required" }, { status: 400 });
    }

    const session = await getServerSession(authOptions);
    const assignerName = session?.user?.name || "Um usuário";

    let assignedUser = null;
    if (userId) {
      assignedUser = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          email: true,
          image: true
        }
      });
    }

    const existingContents = await prisma.content.findMany({
      where: { id: { in: contentIds } },
      include: {
        weeklyActivity: {
          include: {
            monthlyPlanning: {
              include: {
                client: true
              }
            }
          }
        }
      }
    });

    if (existingContents.length !== contentIds.length) {
      return NextResponse.json({ message: "Some content IDs are invalid" }, { status: 400 });
    }

    const updatedContents = await prisma.content.updateMany({
      where: { id: { in: contentIds } },
      data: { 
        assignedToId: userId || null,
        ...(userId ? { status: "repassado" } : {})
      }
    });

    const contentsWithAssignee = await prisma.content.findMany({
      where: { id: { in: contentIds } },
      include: {
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        }
      }
    });

    if (userId && assignedUser) {
      const clientGroups = existingContents.reduce<ClientGroups>((groups, content) => {
        const clientId = content.weeklyActivity.monthlyPlanning.client.id;
        const clientName = content.weeklyActivity.monthlyPlanning.client.name;
        const planningStatus = content.weeklyActivity.monthlyPlanning.status;
        
        if (planningStatus === "aprovado") {
          if (!groups[clientId]) {
            groups[clientId] = {
              name: clientName,
              count: 0
            };
          }
          
          groups[clientId].count++;
        }
        
        return groups;
      }, {});

      for (const clientId in clientGroups) {
        const client = clientGroups[clientId];
        if (client.count > 0) {
          await prisma.notification.create({
            data: {
              content: `${assignerName} atribuiu ${client.count} demanda${client.count > 1 ? 's' : ''} de ${client.name} a você`,
              type: "assigned_demand",
              entityType: "content",
              userId: assignedUser.email
            }
          });
        }
      }
    }

    return NextResponse.json({
      count: updatedContents.count,
      contents: contentsWithAssignee
    });
  } catch (error) {
    console.error("Error updating bulk content assignment:", error instanceof Error ? error.message : String(error));
    return NextResponse.json({ message: "Error updating bulk content assignment" }, { status: 500 });
  }
}
