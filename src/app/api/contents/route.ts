import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { StepType } from "@prisma/client";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

export async function POST(req: Request) {
  try {
    const data = await req.json();
    const {
      weeklyActivityId,
      activityDate,
      contentType,
      channel,
      destination,
      details,
      copywriting,
      reference,
      caption,
      assignedToId,
      steps,
      carouselImagesCount,
      status
    } = data;

    if (!weeklyActivityId || !activityDate || !contentType || !destination) {
      return NextResponse.json(
        { message: "Os campos weeklyActivityId, activityDate, contentType e destination são obrigatórios" },
        { status: 400 }
      );
    }

    const newContent = await prisma.content.create({
      data: {
        weeklyActivityId,
        activityDate: new Date(activityDate),
        contentType,
        channel,
        destination,
        details,
        copywriting,
        reference,
        caption,
        assignedToId,
        status: status || "pendente",
        carouselImagesCount: carouselImagesCount ? parseInt(carouselImagesCount.toString()) : null
      },
      include: {
        assignedTo: true,
        weeklyActivity: {
          include: {
            monthlyPlanning: {
              include: {
                client: true
              }
            }
          }
        }
      }
    });

    if (assignedToId && newContent.assignedTo) {
      const planningStatus = newContent.weeklyActivity?.monthlyPlanning?.status;

      if (planningStatus === "aprovado") {
        const session = await getServerSession(authOptions);
        const assignerName = session?.user?.name || "Um usuário";
        const clientName = newContent.weeklyActivity?.monthlyPlanning?.client?.name;
        const contentDate = new Date(newContent.activityDate).toLocaleDateString('pt-BR');
        const contentType = newContent.contentType;

        await prisma.notification.create({
          data: {
            content: `${assignerName} atribuiu uma demanda de ${clientName} (${contentType}) para ${contentDate} a você`,
            type: "assigned_demand",
            entityId: newContent.id,
            entityType: "content",
            userId: newContent.assignedTo.email
          }
        });
      }
    }

    if (steps && Array.isArray(steps) && steps.length > 0) {
      const session = await getServerSession(authOptions);
      const assignerName = session?.user?.name || "Um usuário";

      await Promise.all(steps.map(async (step) => {
        const { type, assignedToId } = step;

        if (!type || !assignedToId) {
          return;
        }

        const createdStep = await prisma.contentStep.create({
          data: {
            contentId: newContent.id,
            type: type as StepType,
            assignedToId
          },
          include: {
            assignedTo: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true
              }
            }
          }
        });

        if (createdStep.assignedTo) {
          const planningStatus = newContent.weeklyActivity?.monthlyPlanning?.status;

          if (planningStatus === "aprovado") {
            await prisma.notification.create({
              data: {
                content: `${assignerName} atribuiu você à etapa ${createdStep.type} da demanda do cliente ${newContent.weeklyActivity?.monthlyPlanning?.client?.name}`,
                type: "assigned_step",
                entityId: newContent.id,
                entityType: "content_step",
                userId: createdStep.assignedTo.email
              }
            });
          }
        }
      }));
    }

    return NextResponse.json(newContent);
  } catch (error) {
    console.error("Erro ao criar conteúdo:", error);
    return NextResponse.json({ message: "Erro interno do servidor" }, { status: 500 });
  }
}