"use client";

import React, { useEffect, useState } from 'react';
import { Header } from '../components/header';
import { Footer } from '../components/footer';
import { UpdatesInformationOthers } from '../components/updates-information-others';
import { UpdatesInformationAdmin } from '../components/updates-information-admin';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { MoveLeft, Shield, Users } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { Button } from '../components/ui/button';
import { useRouter } from 'next/navigation';

export default function UpdatesPage() {
    const { data: session, status } = useSession();
    const [isAdmin, setIsAdmin] = useState(false);
    const router = useRouter();

    useEffect(() => {
        if (status === "unauthenticated") {
            router.push("/");
        }
    }, [status, router]);

    useEffect(() => {
        const fetchUserRole = async () => {
            try {
                if (session?.user?.email) {
                    const response = await fetch(`/api/users/${session.user.email}`);
                    if (response.ok) {
                        const user = await response.json();
                        setIsAdmin(user?.role === "ADMIN" || user?.role === "DEVELOPER");
                    } else {
                        console.error("Erro ao buscar função do usuário:", await response.json());
                    }
                }
            } catch (error) {
                console.error("Erro ao buscar função do usuário:", error);
            }
        };

        fetchUserRole();
    }, [session]);


    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <main className="container p-4 xs:p-8 flex-grow">
                <Button
                    variant="outline"
                    onClick={() => router.push('/system')}
                >
                    <MoveLeft className="h-4 w-4" />
                    Voltar
                </Button>
                <h2 className="text-lg font-semibold border-b border-zinc-200 dark:border-zinc-800 mt-6">Atualizações recentes</h2>
                <span className='text-sm text-muted-foreground block mt-2 mb-5'>
                    Versão 1.0.2 - 19/06/2025
                </span>
                <div className="flex flex-col gap-4">
                    {isAdmin && (
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-1">
                                    <Shield className="h-4 w-4" />
                                    Admin
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <UpdatesInformationAdmin />
                            </CardContent>
                        </Card>
                    )}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-1">
                                <Users className="h-4 w-4" />
                                Todos
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <UpdatesInformationOthers />
                        </CardContent>
                    </Card>
                </div>
            </main>
            <Footer />
        </div>
    );
}
