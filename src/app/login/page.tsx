"use client";

import { signIn, useSession } from "next-auth/react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardTitle } from "../components/ui/card";
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import Loading from "../components/ui/loading";
import { Ellipsis } from "lucide-react";
import Link from "next/link";

export default function LogInPage() {
    const { status } = useSession();
    const router = useRouter();
    const [isLoading, setIsLoading] = useState(false);
    const [mounted, setMounted] = useState(false);
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [error, setError] = useState("");

    const currentYear = new Date().getFullYear();

    useEffect(() => {
        setMounted(true);
    }, []);

    const handleLoginWithGoogleClick = async () => {
        setIsLoading(true);
        try {
            await signIn("google");
        } finally {
            setIsLoading(false);
        }
    };

    const handleEmailLogin = async () => {
        setIsLoading(true);
        setError("");
        try {
            const result = await signIn("credentials", {
                email,
                password,
                redirect: false,
            });

            if (result?.error) {
                setError(result.error === 'CredentialsSignin' ? 'Credenciais inválidas' : result.error);
                setIsLoading(false);
            } else if (result?.ok) {
                router.push('/dashboard');
            } else {
                setError('Erro inesperado durante o login');
                setIsLoading(false);
            }
        } catch (err) {
            console.error('Erro ao fazer login:', err);
            setError('Erro de conexão');
            setIsLoading(false);
        }
    };

    useEffect(() => {
        if (status === "authenticated") {
            setIsLoading(true);
            router.push("dashboard");
        }
    }, [status, router]);

    if (status === "loading" || isLoading) {
        return (
            <div className="min-h-[100dvh] flex justify-center items-center bg-white login-page">
                <Loading />
            </div>
        );
    }

    if (status === "unauthenticated") {
        return (
            <div className="flex min-h-[100dvh] bg-white text-black login-page">
                <div className="flex-1 sm:mt-24 md:m-0 flex justify-center bg-white">
                    <div className="flex flex-col items-center justify-center">
                        <h2 className="text-lg mb-2">
                            Bem-vindo ao <span className="font-bold">B4Desk</span>
                        </h2>
                        <Image src="/icon-b4desk.svg" alt="B4Desk" width={50} height={50} />
                        <Card className="w-full sm:w-96 p-8 mx-4 mt-8 lg:mt-8 bg-white border-zinc-200">
                            <CardTitle className="text-black mb-2">Faça seu login</CardTitle>
                            <CardDescription className="text-zinc-600">Entre com seu e-mail e senha ou use sua conta do Google</CardDescription>
                            <CardContent className="p-0">
                                {error && <p className="text-sm text-red-500 mt-2">{error}</p>}
                                <div className="space-y-2 mt-2">
                                    <Input
                                        type="email"
                                        placeholder="E-mail"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        required
                                        className="placeholder:text-sm border-neutral-300"
                                    />
                                    <Input
                                        type="password"
                                        placeholder="Senha"
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                        required
                                        className="placeholder:text-sm border-neutral-300"
                                    />
                                </div>
                                <Button
                                    type="button"
                                    className="bg-neutral-950 text-neutral-100 w-full mt-4 hover:bg-neutral-800"
                                    onClick={handleEmailLogin}
                                    disabled={isLoading}
                                >
                                    {isLoading ? <Ellipsis /> : "Entrar com e-mail"}
                                </Button>
                            </CardContent>
                            <div className="flex justify-center my-2">
                                <span className="text-zinc-500 text-sm">ou</span>
                            </div>
                            <CardContent className="p-0">
                                <Button
                                    type="button"
                                    variant="outline"
                                    className="w-full flex items-center gap-0.5 font-bold hover:bg-neutral-100 hover:text-neutral-900 border-neutral-300"
                                    onClick={handleLoginWithGoogleClick}
                                    disabled={isLoading}
                                >
                                    {mounted && (
                                        <Image
                                            src="/google7123025.svg"
                                            alt="Google"
                                            width={28}
                                            height={28} />
                                    )}
                                    <span>
                                        {isLoading ? <Ellipsis /> : "Entrar com Google"}
                                    </span>
                                </Button>
                            </CardContent>
                            <div className="flex justify-center my-3 text-center">
                                <span className="text-zinc-500 text-sm">Não tem uma conta? <Link href="/signup" className="text-blue-600 hover:underline">Cadastre-se</Link></span>
                            </div>
                        </Card>
                    </div>
                    <p className="absolute bottom-4 text-center w-full text-sm text-zinc-500">
                        © {currentYear} B4Desk - B4 Comunicação. Todos os direitos reservados.
                    </p>
                </div>
                <div className="hidden md:block flex-1 bg-gradient-to-bl from-[#db5643] to-[#70aa87]">
                </div>
            </div>
        );
    }

    return null;
}
