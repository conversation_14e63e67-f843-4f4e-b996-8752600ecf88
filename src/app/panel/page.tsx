"use client"

import { MessageSquareMore, <PERSON>, ListTodo, Bell, MoveLeft } from "lucide-react"
import { Footer } from "../components/footer"
import { Header } from "../components/header"
import Link from 'next/link'
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { NotAllowed } from "../components/not-allowed"
import Loading from "../components/ui/loading"
import { Button } from "../components/ui/button"

export default function PanelPage() {
    const { data: session, status } = useSession();
    const [isAdmin, setIsAdmin] = useState(false);
    const [isFetchingRole, setIsFetchingRole] = useState(true);
    const router = useRouter();

    useEffect(() => {
        const fetchUserRole = async () => {
            try {
                if (session?.user?.email) {
                    const response = await fetch(`/api/users/${session.user.email}`);
                    if (response.ok) {
                        const user = await response.json();

                        const hasPermission =
                            user?.role === "ADMIN" ||
                            user?.role === "DEVELOPER";

                        setIsAdmin(hasPermission);
                    } else {
                        console.error("Falha ao buscar dados do usuário:", response.status);
                        setIsAdmin(false);
                    }
                } else {
                    setIsAdmin(false);
                }
            } catch (error) {
                console.error("Erro ao buscar função do usuário:", error);
                setIsAdmin(false);
            } finally {
                setIsFetchingRole(false);
            }
        };

        if (status === "authenticated") {
            fetchUserRole();
        } else if (status === "unauthenticated") {
            router.push("/auth/signin");
        }
    }, [status, session, router]);

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:p-8 flex-grow">
                {isFetchingRole ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : isAdmin ? (
                    <>
                        <Button
                            variant="outline"
                            onClick={() => router.push('/dashboard')}
                        >
                            <MoveLeft className="h-4 w-4" />
                            Voltar
                        </Button>
                        <h2 className="text-lg font-semibold border-b border-zinc-200 dark:border-zinc-800 pb-2 mt-6">
                            Painel administrativo
                        </h2>
                        <div className="mt-4">
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                                Bem-vindo ao painel de controle do sistema. Aqui você pode gerenciar os usuários e feedbacks do sistema e o time de colaboradores.
                            </p>
                            <div className="mt-8 grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <Link href="/admin/demands" className="group flex items-center justify-center p-6 bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-zinc-200 dark:border-zinc-700 hover:border-primary2 dark:hover:border-primary2 transition-colors">
                                    <div className="text-center">
                                        <div className="mb-2 flex justify-center">
                                            <ListTodo size={28} className="text-gray-700 dark:text-gray-300 group-hover:text-primary2 transition-colors" />
                                        </div>
                                        <h3 className="text-lg font-medium">Demandas</h3>
                                        <p className="text-sm text-gray-500 dark:text-gray-400">Gerenciar todas as demandas e atribuições da equipe</p>
                                    </div>
                                </Link>

                                <Link href="/notifications" className="group flex items-center justify-center p-6 bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 hover:border-primary2 dark:hover:border-primary2 transition-colors">
                                    <div className="text-center">
                                        <div className="mb-2 flex justify-center">
                                            <Bell size={28} className="text-gray-700 dark:text-gray-300 group-hover:text-primary2 transition-colors" />
                                        </div>
                                        <h3 className="text-lg font-medium">Notificações</h3>
                                        <p className="text-sm text-gray-500 dark:text-gray-400">Gerenciar todas as notificações do sistema</p>
                                    </div>
                                </Link>

                                <Link href="/admin/users" className="group flex items-center justify-center p-6 bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-zinc-200 dark:border-zinc-700 hover:border-primary2 dark:hover:border-primary2 transition-colors">
                                    <div className="text-center">
                                        <div className="mb-2 flex justify-center">
                                            <Users size={28} className="text-gray-700 dark:text-gray-300 group-hover:text-primary2 transition-colors" />
                                        </div>
                                        <h3 className="text-lg font-medium">Usuários</h3>
                                        <p className="text-sm text-gray-500 dark:text-gray-400">Gerenciar usuários do sistema</p>
                                    </div>
                                </Link>

                                <Link href="/admin/feedbacks" className="group flex items-center justify-center p-6 bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 hover:border-primary2 dark:hover:border-primary2 transition-colors">
                                    <div className="text-center">
                                        <div className="mb-2 flex justify-center">
                                            <MessageSquareMore size={28} className="text-gray-700 dark:text-gray-300 group-hover:text-primary2 transition-colors" />
                                        </div>
                                        <h3 className="text-lg font-medium">Feedbacks</h3>
                                        <p className="text-sm text-gray-500 dark:text-gray-400">Visualizar feedbacks dos usuários</p>
                                    </div>
                                </Link>
                            </div>
                        </div>
                    </>
                ) : (
                    <NotAllowed
                        page="/"
                    />
                )}
            </div>
            <Footer />
        </div>
    )
}
