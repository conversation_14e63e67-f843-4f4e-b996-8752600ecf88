"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import { Header } from "@/app/components/header";
import { Footer } from "@/app/components/footer";
import { But<PERSON> } from "@/app/components/ui/button";
import { MoveLeft, Package, AppWindowMac, InfoIcon } from "lucide-react";
import Loading from "@/app/components/ui/loading";
import { NotAllowed } from "@/app/components/not-allowed";
import Link from "next/link";
import Image from "next/image";
import { DeliveriesContent } from "@/app/components/deliveries-content";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/app/components/ui/dialog";
import { Separator } from "@/app/components/ui/separator";
import { Badge } from "@/app/components/ui/badge";
import { ClientNavigationModal } from "@/app/components/client-navigation-modal";

interface Client {
    id: string;
    name: string;
    instagramUsername?: string;
    [key: string]: unknown;
}

export default function DeliveriesPage() {
    const { data: session, status } = useSession();
    const [client, setClient] = useState<Client | null>(null);
    const [isFetchingRole, setIsFetchingRole] = useState(true);
    const [clientNavigationOpen, setClientNavigationOpen] = useState(false);
    const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
    const [viewedClients, setViewedClients] = useState<{ id: string; name: string }[]>([]);
    const router = useRouter();
    const params = useParams();
    const id = params?.id as string;

    const handleOpenClientNavigation = (clientId: string) => {
        setSelectedClientId(clientId);
        setClientNavigationOpen(true);
    };

    useEffect(() => {
        if (status === "unauthenticated") {
            router.push("/login");
        }
    }, [status, router]);

    useEffect(() => {
        const recentClients = JSON.parse(localStorage.getItem('viewedClients') || '[]');
        setViewedClients(recentClients);
    }, []);

    useEffect(() => {
        if (id && session?.user) {
            fetch(`/api/clients/${id}`)
                .then(res => res.json())
                .then(data => {
                    setClient(data);
                    setIsFetchingRole(false);
                })
                .catch(err => {
                    console.error("Erro ao carregar dados do cliente:", err);
                    setIsFetchingRole(false);
                });
        } else {
            setIsFetchingRole(false);
        }
    }, [id, session]);

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:px-8 xs:pt-4 xs:pb-8 flex-grow">
                {isFetchingRole || !client ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : session?.user ? (
                    <>
                        <div className="flex justify-between items-center">
                            <div className="flex items-start xs:items-center justify-between w-full gap-2">
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant="outline"
                                        size="icon"
                                        onClick={() => router.push('/clients')}
                                    >
                                        <MoveLeft className="h-4 w-4" />
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="icon"
                                        onClick={() => handleOpenClientNavigation(client.id)}
                                    >
                                        <AppWindowMac className="h-4 w-4" />
                                    </Button>
                                </div>
                                <ClientNavigationModal
                                    open={clientNavigationOpen}
                                    onOpenChange={setClientNavigationOpen}
                                    clientId={selectedClientId}
                                    clientName={viewedClients.find(client => client.id === selectedClientId)?.name}
                                />
                                <div className="flex flex-col xs:flex-row items-end xs:items-center gap-2 group">
                                    <h1 className="text-sm xs:text-lg uppercase font-geistMono font-semibold tracking-tight">
                                        Entregas
                                    </h1>
                                    <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                                        <Package size={24} color="#db5743" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h2 className="mt-4 text-lg font-semibold">{client.name}</h2>
                            {client.instagramUsername && (
                                <Link
                                    href={`https://www.instagram.com/${client.instagramUsername}`}
                                    target="_blank"
                                    className="text-sm text-blue-500 hover:underline underline-offset-4 cursor-pointer inline-flex items-center gap-1"
                                >
                                    <Image src="/instagram.svg" width={14} height={14} alt="Instagram" />
                                    {client.instagramUsername}
                                </Link>
                            )}
                            <div className="border-t pt-1 border-zinc-200 dark:border-zinc-800 mt-2">
                                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-1 mb-2">
                                    <h3 className="flex items-center gap-1 text-sm font-semibold">
                                        <Package size={16} />
                                        Entregas
                                    </h3>
                                    <div className="flex items-center gap-1">
                                        <Badge variant="secondary">
                                            etapa 7
                                        </Badge>
                                        <Badge variant="secondary">
                                            entrega de conteúdo
                                        </Badge>
                                        <Dialog>
                                            <DialogTrigger asChild>
                                                <Button variant="link" size="icon">
                                                    <InfoIcon />
                                                </Button>
                                            </DialogTrigger>
                                            <DialogContent
                                                className="h-[85vh] p-0 overflow-y-auto"
                                                style={{ maxWidth: "550px !important", width: "550px !important" }}
                                            >
                                                <div className="p-6">
                                                    <DialogHeader className="mb-4">
                                                        <DialogTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                                                            Saiba mais
                                                        </DialogTitle>
                                                        <DialogDescription className="font-medium text-gray-700 dark:text-gray-200">
                                                            Etapa 7 — Entrega de Conteúdo
                                                            <span className="block mt-1 text-sm font-normal">Visualização de entregas</span>
                                                        </DialogDescription>
                                                    </DialogHeader>
                                                    <div className="space-y-4 text-sm">
                                                        <div>
                                                            <p className="mb-2">Visualização de Entregas:</p>
                                                            <ul className="list-disc pl-5 space-y-1">
                                                                <li>Esta página exibe todas as atividades do planejamento mensal aprovado.</li>
                                                                <li>Você pode filtrar as entregas por mês usando o seletor no canto superior direito.</li>
                                                                <li>Cada card mostra o título, data, imagem e legenda da atividade.</li>
                                                            </ul>
                                                        </div>
                                                        <Separator className="my-2" />
                                                        <div>
                                                            <p className="mb-2">Exportação para PDF:</p>
                                                            <p className="text-sm">
                                                                Clique no botão de download para exportar as entregas do mês selecionado em formato PDF.
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </DialogContent>
                                        </Dialog>
                                    </div>
                                </div>
                                <DeliveriesContent clientId={id} client={client} />
                            </div>
                        </div>
                    </>
                ) : (
                    <NotAllowed page="clients" />
                )}
            </div>
            <Footer />
        </div>
    );
}
