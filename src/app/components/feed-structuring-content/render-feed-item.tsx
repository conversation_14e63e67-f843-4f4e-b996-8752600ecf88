import Image from 'next/image';

interface FeedContent {
    id: string;
    contentType?: string;
    details?: string;
    urlStructuringFeed?: string | string[];
    activityDate: string | Date;
    week: number;
    month?: number;
    year?: number;
    status?: string;
    planningId?: string;
}

export const extractGoogleDriveId = (url: string | string[]): string | null => {
    if (!url) return null;
    if (Array.isArray(url)) return extractGoogleDriveId(url[0]);

    const match1 = url.match(/\/d\/([^/]+)/);
    if (match1) return match1[1];

    const match2 = url.match(/id=([^&]+)/);
    if (match2) return match2[1];

    return null;
};

export const renderFeedItem = (content: FeedContent, isPdfMode = false, compactView = false) => {
    const driveId = content.urlStructuringFeed ? extractGoogleDriveId(content.urlStructuringFeed) : null;

    if (isPdfMode) {
        return (
            <>
                {content.urlStructuringFeed ? (
                    <Image
                        src={driveId ? `/api/drive-proxy?id=${driveId}&quality=medium&size=medium` : '/images/placeholder.png'}
                        alt="Visualização do feed"
                        fill
                        sizes="(max-width: 280px) 100vw, 280px"
                        className="object-cover rounded"
                        priority
                    />
                ) : (
                    <div className="w-full h-full flex items-center justify-center bg-amber-100 text-amber-800 text-xs text-center p-2 rounded">
                        * URL pendente ou não é necessária
                    </div>
                )}
            </>
        );
    }

    return (
        <div className="border rounded-md p-3">
            {content.details && (
                <p className="text-sm mb-2">{content.details}</p>
            )}

            {content.urlStructuringFeed ? (
                <div className={`flex flex-col ${compactView ? 'h-[220px]' : 'h-[240px]'}`}>
                    <div className={`flex-1 bg-gray-100 rounded relative ${compactView ? 'h-[220px]' : 'h-[240px]'} flex items-center justify-center`}>
                        <Image
                            src={driveId ? `/api/drive-proxy?id=${driveId}&quality=high&size=large` : '/images/placeholder.png'}
                            alt="Visualização do feed"
                            width={800}
                            height={600}
                            className="max-w-full max-h-full object-contain"
                        />
                    </div>

                    <div className="flex items-center gap-1 mt-4 text-xs text-blue-500">
                        <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width={12} height={12} fill="oklch(62.3% 0.214 259.815)"><title>Google Drive</title><path d="M12.01 1.485c-2.082 0-3.754.02-3.743.047.01.02 1.708 3.001 3.774 6.62l3.76 6.574h3.76c2.081 0 3.753-.02 3.742-.047-.005-.02-1.708-3.001-3.775-6.62l-3.76-6.574zm-4.76 1.73a789.828 789.861 0 0 0-3.63 6.319L0 15.868l1.89 3.298 1.885 3.297 3.62-6.335 3.618-6.33-1.88-3.287C8.1 4.704 7.255 3.22 7.25 3.214zm2.259 12.653-.203.348c-.114.198-.96 1.672-1.88 3.287a423.93 423.948 0 0 1-1.698 2.97c-.01.026 3.24.042 7.222.042h7.244l1.796-3.157c.992-1.734 1.85-3.23 1.906-3.323l.104-.167h-7.249z" /></svg>
                        <a href={content.urlStructuringFeed as string} target="_blank" rel="noopener noreferrer" className="hover:underline">
                            Ver no Google Drive
                        </a>
                    </div>
                </div>
            ) : (
                <div className={`text-amber-800 border border-dashed p-1 rounded text-xs text-center ${compactView ? 'h-[220px]' : 'h-[240px]'} flex items-center justify-center`}>
                    * URL pendente ou não é necessária
                </div>
            )}
        </div>
    );
};
