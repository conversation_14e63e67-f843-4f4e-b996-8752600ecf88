"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, Card<PERSON><PERSON>le, CardFooter } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Separator } from "@/app/components/ui/separator";
import { Checkbox } from "@/app/components/ui/checkbox";
import {
    Check,
    RefreshCw,
    Edit,
    MoveUpRight,
    Ellipsis,
    Calendar,
    Download,
    ListChecks,
    X,
    CheckSquare,
    Square
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import Loading from "../ui/loading";
import { format } from "date-fns";
import { pt } from "date-fns/locale";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from "@/app/components/ui/dialog";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from "@/app/components/ui/select";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { Client } from "@prisma/client";
import { renderFeedItem } from "./render-feed-item";

interface Content {
    id: string;
    activityDate: string;
    contentType: string;
    channel: string;
    details: string;
    destination: string;
    caption?: string;
    status?: string;
    copywriting?: string;
    reference?: string;
    urlStructuringFeed?: string;
    urlTypes?: string[];
    urlMediaTypes?: string[];
}

interface WeeklyActivity {
    id: string;
    description: string;
    week: number;
    contents: Content[];
}

interface MonthlyPlanning {
    status: string;
    id: string;
    month: number;
    year: number;
    activities: WeeklyActivity[];
}

interface FeedStructuringContentProps {
    clientId: string;
    refreshTrigger?: number;
    client?: Client | null;
    isPdfMode?: boolean;
    initialMonth?: string;
    compactView?: boolean;
}

export const FeedStructuringContent = ({
    clientId,
    refreshTrigger = 0,
    isPdfMode = false,
    initialMonth,
}: FeedStructuringContentProps) => {
    const [inProgressContents, setInProgressContents] = useState<Array<Content & { planningId: string, week: number, month: number, year: number }>>([]);
    const [filteredContents, setFilteredContents] = useState<Array<Content & { planningId: string, week: number, month: number, year: number }>>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [selectedContent, setSelectedContent] = useState<string | null>(null);
    const [urlInput, setUrlInput] = useState("");
    const [isUpdatingUrl, setIsUpdatingUrl] = useState(false);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [availableMonths, setAvailableMonths] = useState<Array<{ label: string, value: string }>>([]);
    const [selectedMonth, setSelectedMonth] = useState<string | null>(null);
    const [isMultiSelectMode, setIsMultiSelectMode] = useState(false);
    const [selectedContents, setSelectedContents] = useState<string[]>([]);
    const [isUpdatingMultiple, setIsUpdatingMultiple] = useState(false);

    const fetchInProgressContents = useCallback(async () => {
        try {
            const response = await fetch(`/api/clients/${clientId}?include=monthlyPlannings.activities.contents`);

            if (!response.ok) {
                throw new Error(`Error fetching client data: ${response.status}`);
            }

            const data = await response.json();
            const plannedContents: Array<Content & { planningId: string, week: number, month: number, year: number }> = [];
            const monthsSet = new Set<string>();

            if (data.monthlyPlannings && Array.isArray(data.monthlyPlannings)) {
                data.monthlyPlannings.forEach((planning: MonthlyPlanning & { showActivitiesInModuleThree: boolean }) => {
                    if (planning.status === "aprovado") {
                        const monthKey = `${planning.month}-${planning.year}`;
                        let hasInProgressContent = false;

                        planning.activities.forEach((activity: WeeklyActivity) => {
                            if (activity.contents && Array.isArray(activity.contents) && planning.showActivitiesInModuleThree) {
                                activity.contents.forEach((content: Content) => {
                                    const VALID_DESTINATIONS = ["Feed", "Story/Feed"];

                                    if (
                                        content.status === "estruturação de feed" &&
                                        VALID_DESTINATIONS.includes(content.destination)
                                    ) {
                                        plannedContents.push({
                                            ...content,
                                            planningId: planning.id,
                                            week: activity.week,
                                            month: planning.month,
                                            year: planning.year
                                        });
                                        hasInProgressContent = true;
                                    }
                                });
                            }
                        });

                        if (hasInProgressContent) {
                            monthsSet.add(monthKey);
                        }
                    }
                });
            }

            plannedContents.sort((a, b) => {
                return new Date(b.activityDate).getTime() - new Date(a.activityDate).getTime();
            });

            const monthsArray = Array.from(monthsSet).map(monthKey => {
                const [month, year] = monthKey.split('-').map(Number);
                const date = new Date(year, month - 1);

                return {
                    value: monthKey,
                    label: format(date, 'MMMM yyyy', { locale: pt }).charAt(0).toUpperCase() +
                        format(date, 'MMMM yyyy', { locale: pt }).slice(1)
                };
            });

            monthsArray.sort((a, b) => {
                const [monthA, yearA] = a.value.split('-').map(Number);
                const [monthB, yearB] = b.value.split('-').map(Number);

                if (yearA !== yearB) return yearB - yearA;
                return monthB - monthA;
            });

            setInProgressContents(plannedContents);
            setAvailableMonths(monthsArray);

            if (selectedMonth) {
                filterContentsByMonth(plannedContents, selectedMonth);
            }
            else if (monthsArray.length > 0) {
                const currentDate = new Date();
                const currentMonth = currentDate.getMonth() + 1;
                const currentYear = currentDate.getFullYear();
                const currentMonthKey = `${currentMonth}-${currentYear}`;
                const currentMonthExists = monthsArray.some(month => month.value === currentMonthKey);

                if (currentMonthExists) {
                    setSelectedMonth(currentMonthKey);
                    filterContentsByMonth(plannedContents, currentMonthKey);
                } else {
                    setSelectedMonth(monthsArray[0].value);
                    filterContentsByMonth(plannedContents, monthsArray[0].value);
                }
            } else {
                setFilteredContents(plannedContents);
            }
        } catch (error) {
            console.error("Error fetching feed content:", error);
            toast.error("Erro ao carregar conteúdos em andamento");
        } finally {
            setIsLoading(false);
            setIsRefreshing(false);
        }
    }, [clientId, selectedMonth]);

    const filterContentsByMonth = (contents: Array<Content & { planningId: string, week: number, month: number, year: number }>, monthKey: string) => {
        if (!monthKey) {
            setFilteredContents(contents);
            return;
        }

        const [month, year] = monthKey.split('-').map(Number);
        const filtered = contents.filter(
            content => content.month === month && content.year === year
        );

        setFilteredContents(filtered);
    };

    useEffect(() => {
        fetchInProgressContents();
    }, [fetchInProgressContents, refreshTrigger]);

    useEffect(() => {
        if (initialMonth) {
            setSelectedMonth(initialMonth);
        }
    }, [initialMonth, selectedMonth]);

    const handleMonthChange = (value: string) => {
        setSelectedMonth(value);
        filterContentsByMonth(inProgressContents, value);
    };

    const handleRefresh = () => {
        setIsRefreshing(true);
        fetchInProgressContents();
    };

    const toggleCompleteStatus = async (contentId: string, currentStatus: string) => {
        const completedStatuses = ["feed estruturado", "repassado", "em revisão", "pend. captação", "captado", "concluído"];
        
        const newStatus = completedStatuses.includes(currentStatus) ? "em andamento" : "feed estruturado";

        try {
            const response = await fetch(`/api/contents/${contentId}`, {
                method: "PATCH",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ status: newStatus }),
            });

            if (!response.ok) {
                throw new Error(`Error updating content status: ${response.status}`);
            }

            if (newStatus === "feed estruturado") {
                toast.success("Marcado como concluído");
            } else {
                toast.success("Marcado como em andamento");
            }

            fetchInProgressContents();
        } catch (error) {
            console.error("Error updating content status:", error);
            toast.error("Erro ao atualizar status do conteúdo");
        }
    };

    const markMultipleAsComplete = async () => {
        if (selectedContents.length === 0) {
            toast.error("Nenhum conteúdo selecionado");
            return;
        }

        setIsUpdatingMultiple(true);
        let successCount = 0;
        let errorCount = 0;

        try {
            const promises = selectedContents.map(async (contentId) => {
                try {
                    const response = await fetch(`/api/contents/${contentId}`, {
                        method: "PATCH",
                        headers: {
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({ status: "feed estruturado" }),
                    });

                    if (!response.ok) {
                        throw new Error(`Error updating content status: ${response.status}`);
                    }
                    successCount++;
                } catch (error) {
                    console.error(`Error updating content ${contentId}:`, error);
                    errorCount++;
                }
            });

            await Promise.all(promises);

            if (successCount > 0) {
                toast.success(`${successCount} conteúdo(s) marcado(s) como concluído(s)`);
            }
            if (errorCount > 0) {
                toast.error(`Erro ao atualizar ${errorCount} conteúdo(s)`);
            }

            setSelectedContents([]);
            setIsMultiSelectMode(false);
            fetchInProgressContents();
        } catch (error) {
            console.error("Error in batch update:", error);
            toast.error("Erro ao processar atualização em lote");
        } finally {
            setIsUpdatingMultiple(false);
        }
    };

    const toggleContentSelection = (contentId: string) => {
        setSelectedContents(prev => {
            if (prev.includes(contentId)) {
                return prev.filter(id => id !== contentId);
            } else {
                return [...prev, contentId];
            }
        });
    };

    const toggleMultiSelectMode = () => {
        setIsMultiSelectMode(prev => !prev);
        if (isMultiSelectMode) {
            setSelectedContents([]);
        }
    };

    const toggleSelectAll = () => {
        if (selectedContents.length === filteredContents.length) {
            setSelectedContents([]);
        } else {
            setSelectedContents(filteredContents.map(content => content.id));
        }
    };

    const areAllSelected = useMemo(() => {
        return filteredContents.length > 0 && selectedContents.length === filteredContents.length;
    }, [filteredContents.length, selectedContents.length]);

    const updateStructuringFeedUrl = async () => {
        if (!selectedContent) return;

        setIsUpdatingUrl(true);

        try {
            if (urlInput && !urlInput.includes('drive.google.com')) {
                toast.error("Por favor, insira uma URL válida do Google Drive");
                return;
            }

            const response = await fetch(`/api/contents/${selectedContent}/url-structuring`, {
                method: "PATCH",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ urlStructuringFeed: urlInput }),
            });

            if (!response.ok) {
                throw new Error(`Error updating URL: ${response.status}`);
            }

            toast.success("URL atualizada com sucesso");
            fetchInProgressContents();
            setIsDialogOpen(false);
            setUrlInput("");
        } catch (error) {
            console.error("Error updating URL:", error);
            toast.error("Erro ao atualizar URL");
        } finally {
            setIsUpdatingUrl(false);
        }
    };

    const openUrlDialog = (contentId: string, currentUrl?: string) => {
        setSelectedContent(contentId);
        setUrlInput(currentUrl || "");
        setIsDialogOpen(true);
    };

    const renderContentCard = (content: Content & { planningId: string, week: number, month: number, year: number }) => {
        const dateObj = new Date(content.activityDate);
        const formattedDate = format(dateObj, 'dd/MM/yyyy');

        return (
            <Card key={content.id} className="flex flex-col h-full">
                <CardHeader className="px-3 py-2">
                    <div className="flex items-center gap-2">
                        {isMultiSelectMode && (
                            <Checkbox
                                checked={selectedContents.includes(content.id)}
                                onCheckedChange={() => toggleContentSelection(content.id)}
                                className="mr-1"
                            />
                        )}
                        <h3 className="font-medium text-sm">{content.contentType}</h3>
                        <Badge variant="outline" className="font-normal text-xs">
                            {formattedDate}
                        </Badge>
                        <Badge variant="secondary">Semana {content.week}</Badge>
                        <Badge variant="secondary">#{content.id.substring(0, 8).toUpperCase()}</Badge>
                    </div>
                </CardHeader>
                <CardContent className="py-2 px-3 flex-grow">
                    {renderFeedItem(content, isPdfMode)}
                </CardContent>

                {!isPdfMode && (
                    <CardFooter className="px-3 py-2 flex justify-between">
                        <div>
                            <span className={`${["feed estruturado", "repassado", "em revisão", "pend. captação", "captado", "concluído"].includes(content.status || "") ? "text-green-600" : "text-amber-700"} text-xs italic`}>
                                {["feed estruturado", "repassado", "em revisão", "pend. captação", "captado", "concluído"].includes(content.status || "") ? "Concluído" : "Em andamento"}
                            </span>
                        </div>
                        <div className="flex gap-1">
                            <Button
                                variant="outline"
                                size="icon"
                                onClick={() => openUrlDialog(content.id, content.urlStructuringFeed)}
                                title="Editar link da estruturação"
                            >
                                <Edit size={14} />
                            </Button>

                            <Link href={`/monthly-planning/${clientId}`} rel="noopener noreferrer">
                                <Button
                                    variant="outline"
                                    size="icon"
                                    title="Ver no planejamento"
                                >
                                    <MoveUpRight size={14} />
                                </Button>
                            </Link>

                            {!isMultiSelectMode && (
                                <Button
                                    variant={["feed estruturado", "repassado", "em revisão", "pend. captação", "captado", "concluído"].includes(content.status || "") ? "success" : "outline"}
                                    size="icon"
                                    onClick={() => toggleCompleteStatus(content.id, content.status || "")}
                                    title={["feed estruturado", "repassado", "em revisão", "pend. captação", "captado", "concluído"].includes(content.status || "") ? "Remover marcação de concluído" : "Marcar como concluído"}
                                >
                                    <Check
                                        size={14}
                                        className={["feed estruturado", "repassado", "em revisão", "pend. captação", "captado", "concluído"].includes(content.status || "") ? "text-green-500 dark:text-green-200" : "text-gray-600 dark:text-gray-200"}
                                    />
                                </Button>
                            )}
                        </div>
                    </CardFooter>
                )}
            </Card>
        );
    };

    if (isLoading) {
        return (
            <div className="py-8 flex justify-center items-center">
                <Loading />
            </div>
        );
    }

    return (
        <>
            <Card className={`w-full ${isPdfMode ? 'mt-1 border-none shadow-none' : 'mt-4'}`}>
                {!isPdfMode ? (
                    <CardHeader className="flex flex-col sm:flex-row sm:items-center justify-between py-3 gap-3">
                        <div className="flex flex-col xs:flex-row gap-2 items-start xs:items-center">
                            <CardTitle>Conteúdos com destino <i>Feed</i> ou <i>Story/Feed</i></CardTitle>

                            {!isPdfMode && availableMonths.length > 0 && (
                                <Select value={selectedMonth || undefined} onValueChange={handleMonthChange}>
                                    <SelectTrigger className="w-44">
                                        <Calendar size={16} />
                                        <SelectValue placeholder="Selecione o mês" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {availableMonths.map((month) => (
                                            <SelectItem key={month.value} value={month.value}>
                                                {month.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            )}
                        </div>

                        {!isPdfMode && (
                            <div className="flex flex-col gap-2 items-end">
                                <div className="flex items-center gap-2">
                                    {filteredContents.length > 0 && !isMultiSelectMode && (
                                        <a
                                            href={`/api/generate-feed-pdf?id=${clientId}${selectedMonth ? `&month=${selectedMonth}` : ''}`}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            <Button
                                                variant="outline"
                                                size="icon"
                                                title="Exportar para PDF"
                                            >
                                                <Download className="h-4 w-4" />
                                            </Button>
                                        </a>
                                    )}

                                    {filteredContents.length > 0 && (
                                        <Button
                                            variant={isMultiSelectMode ? "destructive" : "outline"}
                                            size="icon"
                                            onClick={toggleMultiSelectMode}
                                            title={isMultiSelectMode ? "Cancelar seleção" : "Selecionar múltiplos"}
                                        >
                                            {isMultiSelectMode ? <X className="h-4 w-4" /> : <ListChecks className="h-4 w-4" />}
                                        </Button>
                                    )}

                                    {isMultiSelectMode && (
                                        <Button
                                            variant="outline"
                                            onClick={toggleSelectAll}
                                            title={areAllSelected ? "Desmarcar todos" : "Selecionar todos"}
                                            className="text-xs px-2 py-1 h-9"
                                        >
                                            {areAllSelected ? (
                                                <CheckSquare className="h-4 w-4 mr-1" />
                                            ) : (
                                                <Square className="h-4 w-4 mr-1" />
                                            )}
                                            {areAllSelected ? "Desmarcar todos" : "Selecionar todos"}
                                        </Button>
                                    )}

                                    {isMultiSelectMode && selectedContents.length > 0 && (
                                        <Button
                                            variant="success"
                                            onClick={markMultipleAsComplete}
                                            disabled={isUpdatingMultiple}
                                            title="Marcar selecionados como concluídos"
                                            className="text-xs px-2 py-1 h-9"
                                        >
                                            {isUpdatingMultiple ? (
                                                <Ellipsis className="h-4 w-4 mr-1" />
                                            ) : (
                                                <Check className="h-4 w-4 mr-1" />
                                            )}
                                            Marcar {selectedContents.length} como concluído
                                        </Button>
                                    )}

                                    <Button
                                        variant="outline"
                                        size="icon"
                                        onClick={handleRefresh}
                                        disabled={isRefreshing}
                                        title="Atualizar"
                                    >
                                        <RefreshCw className={`${isRefreshing ? 'animate-spin' : ''}`} />
                                    </Button>
                                </div>
                            </div>
                        )}
                    </CardHeader>
                ) : null}

                <CardContent className={`${isPdfMode ? 'p-0' : ''}`}>
                    {filteredContents.length > 0 ? (
                        <div className={`${isPdfMode ? 'pdf-grid' : 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'}`}>
                            {isPdfMode
                                ? filteredContents.map((content, index) => (
                                    <div
                                        key={content.id}
                                        className={`pdf-card ${index < 6 ? 'first-page-item' : ''}`}
                                    >
                                        <div className="border rounded-md p-1">
                                            <div className="flex justify-between items-center text-[10px] mb-1">
                                                <span className="font-medium truncate">{content.contentType}</span>
                                                <span className="text-gray-500">S{content.week}</span>
                                            </div>
                                            <div className="pdf-image-container">
                                                {renderFeedItem(content, isPdfMode, true)}
                                            </div>
                                            <div className="text-gray-500 text-[9px] mt-1">
                                                {format(new Date(content.activityDate), 'dd/MM/yyyy')}
                                            </div>
                                        </div>
                                    </div>
                                ))
                                : filteredContents.map(renderContentCard)
                            }
                        </div>
                    ) : (
                        <div className="text-center py-8">
                            <p className="text-muted-foreground">
                                Nenhum conteúdo encontrado para o período selecionado.
                            </p>
                            <Separator className="my-4" />
                            <div className="text-sm text-muted-foreground mt-1">
                                Os conteúdos aparecem aqui quando estão com status{" "}
                                <Badge variant="secondary">estruturação de feed</Badge> e
                                destinados ao Feed ou Story/Feed em planejamentos que são aprovados e exibem atividades no módulo 3.
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>

            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogContent className="max-w-md">
                    <DialogHeader>
                        <DialogTitle>
                            {urlInput ? "Editar link da estruturação" : "Adicionar link da estruturação"}
                        </DialogTitle>
                    </DialogHeader>
                    <div>
                        <Label htmlFor="url-input" className="text-sm font-medium">
                            URL do Google Drive
                        </Label>
                        <Input
                            id="url-input"
                            value={urlInput}
                            onChange={(e) => setUrlInput(e.target.value)}
                            placeholder="https://drive.google.com/file/d/..."
                            className="mt-1.5"
                        />
                        <p className="text-xs text-muted-foreground mt-2">
                            Cole a URL do Google Drive onde está a visualização do feed estruturado.
                        </p>
                    </div>
                    <DialogFooter>
                        <Button
                            variant="secondary"
                            onClick={() => setIsDialogOpen(false)}
                        >
                            Cancelar
                        </Button>
                        <Button
                            onClick={updateStructuringFeedUrl}
                            disabled={isUpdatingUrl}
                            className="mb-2 sm:mb-0"
                        >
                            {isUpdatingUrl ? (
                                <>
                                    <Ellipsis />
                                </>
                            ) : (
                                <>
                                    Salvar URL
                                </>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
};