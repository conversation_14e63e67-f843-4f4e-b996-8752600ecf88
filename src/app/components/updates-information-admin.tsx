export function UpdatesInformationAdmin() {
    return (
        <div className="space-y-2">
            {/* <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 flex-shrink-0 rounded-full bg-primary2"></div>
                <p className="text-sm"><PERSON><PERSON><PERSON>, você tem a opção de escolher múltiplos responsáveis para as demandas de conteúdo.</p>
            </div>
            <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 flex-shrink-0 rounded-full bg-primary2"></div>
                <p className="text-sm">Os planejamentos mensais só irão para o módulo de estruturação de feed quando for ativado o botão <span className="italic font-semibold text-muted-foreground">Exibir atividades no módulo de estruturação de feed</span>.</p>
            </div>
            <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 flex-shrink-0 rounded-full bg-primary2"></div>
                <p className="text-sm">Agora, ao adiconar uma atividade semanal do tipo Story, você pode definir a quantidade de imagens que serão contabilizadas como Stories no limite mensal.</p>
            </div>
            <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 flex-shrink-0 rounded-full bg-primary2"></div>
                <p className="text-sm">Opção de excluir demandas de conteúdo e demandas pontuais em massa na página de gerenciamento de demandas.</p>
            </div>
            <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 flex-shrink-0 rounded-full bg-primary2"></div>
                <p className="text-sm">Possibidade de arquivar demandas pontuais e de conteúdos.</p>
            </div>
            <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 flex-shrink-0 rounded-full bg-primary2"></div>
                <p className="text-sm">Ao arquivar um cliente, todas as demandas e conteúdos relacionados a ele também serão arquivados.</p>
            </div> */}
            <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 flex-shrink-0 rounded-full bg-primary2"></div>
                <p className="text-sm">Ao atribuir demandas, o status será automaticamente atualizado para &quot;Repassado&quot;.</p>
            </div>
            <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 flex-shrink-0 rounded-full bg-primary2"></div>
                <p className="text-sm">Os usuários só receberão notificações de demandas atribuídas a eles se o status do planejamento mensal for &quot;Aprovado&quot;.</p>
            </div>
            <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 flex-shrink-0 rounded-full bg-primary2"></div>
                <p className="text-sm">Agora, você pode solicitar uma alteração em uma demanda, enviando um comentário. Ao adicionar a alteração, o status da demanda será automaticamente atualizado para &quot;Alteração&quot; e o usuário responsável será notificado.</p>
            </div>
            <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 flex-shrink-0 rounded-full bg-primary2"></div>
                <p className="text-sm">
                    O sistema pode ser instalado como um aplicativo móvel em seu dispositivo. Para isso, basta clicar no botão de download no canto direito na parte de baixo da tela.
                </p>
            </div>
            <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 flex-shrink-0 rounded-full bg-primary2"></div>
                <p className="text-sm">
                    Novo módulo de perfil do cliente! Agora, você pode adicionar mais informações sobre os clientes.
                </p>
            </div>
        </div>
    );
}
