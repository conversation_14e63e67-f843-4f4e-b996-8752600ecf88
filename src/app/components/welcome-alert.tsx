import { useState, useEffect } from "react";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
    DialogFooter
} from "@/app/components/ui/dialog";
import { Button } from "@/app/components/ui/button";
import { Separator } from "@/app/components/ui/separator";

interface WelcomeAlertProps {
    userId: string;
    userName?: string;
}

export function WelcomeAlert({ userId, userName }: WelcomeAlertProps) {
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        const hasSeenWelcome = localStorage.getItem(`welcome-seen-${userId}`);

        if (!hasSeenWelcome) {
            setIsOpen(true);
        }
    }, [userId]);

    const handleClose = () => {
        localStorage.setItem(`welcome-seen-${userId}`, "true");
        setIsOpen(false);
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>
                        Boas-vindas ao B4Desk! 🎉
                    </DialogTitle>
                    <DialogDescription className="pt-2">
                        Olá <span className="font-medium">{userName || "usuário"}</span>, ficamos felizes em ver você por aqui!
                    </DialogDescription>
                </DialogHeader>
                <Separator />
                <div>
                    <p className="text-sm text-zinc-700 dark:text-zinc-300">
                        Este sistema foi desenvolvido para otimizar nossos fluxos de trabalho e melhorar
                        a produtividade da nossa equipe.
                    </p>
                    <p className="text-sm mt-4 text-zinc-700 dark:text-zinc-300">
                        Navegue pelo menu para descobrir todas as funcionalidades disponíveis para você e
                        aproveite ao máximo nossa plataforma.
                    </p>
                    <p className="text-sm mt-4 text-zinc-700 dark:text-zinc-300">
                        Caso tenha alguma dúvida, sugestão ou queira reportar um bug, fique à vontade para nos contatar.
                    </p>
                </div>

                <DialogFooter>
                    <Button onClick={handleClose} className="w-full sm:w-auto">
                        Entendi
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}