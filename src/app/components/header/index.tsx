"use client"

import Image from 'next/image';
import { Bell, Cog, Home, LayoutDashboard, ListTodo, LogOut, Menu, MonitorCog, PanelTop, User, Users } from 'lucide-react';
import { Button } from '../ui/button';
import { Sheet, SheetContent, SheetTitle, SheetTrigger } from '../ui/sheet';
import Link from 'next/link';
import { signOut, useSession } from 'next-auth/react';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { useEffect, useState } from 'react';
import { Separator } from '../ui/separator';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '../ui/alert-dialog';
import { ThemeToggle } from '../ui/theme-toggle';
import { useTheme } from 'next-themes';
import { Notifications } from '../notifications';

export const Header = () => {
    const { data: session, status } = useSession();
    const [userRole, setUserRole] = useState('');
    const { resolvedTheme } = useTheme();
    const [mounted, setMounted] = useState(false);

    useEffect(() => {
        setMounted(true);
    }, []);

    useEffect(() => {
        if (status === "authenticated") {
            const fetchUserData = async () => {
                try {
                    if (session?.user?.email) {
                        const email = session.user.email;

                        const response = await fetch(`/api/users/${email}`, {
                            cache: 'no-store'
                        });
                        
                        if (response.ok) {
                            const user = await response.json();
                            setUserRole(user?.role || '');
                        } else {
                            console.error(`Failed to fetch user role. Status: ${response.status}`);
                            const errorResponse = await response.json();
                            console.error("Error response:", errorResponse);
                        }
                    } else {
                        console.error("No email found in session.");
                    }
                } catch (error) {
                    console.error("Error fetching user role:", error);
                }
            };

            fetchUserData();
        }
    }, [status, session]);

    const handleSignOutClick = () => signOut();

    return (
        <header className='flex items-center p-4 xs:px-8 xs:py-4 justify-between border-b border-zinc-200 dark:border-zinc-800'>
            <div className='flex items-center gap-1'>
                <Sheet>
                    <SheetTrigger asChild>
                        <Button variant="outline" size="icon">
                            <Menu size={24} />
                        </Button>
                    </SheetTrigger>
                    <SheetContent side="left">
                        <SheetTitle className='left-9'>
                            <Link href="/">
                                <Image src='/icon-b4desk.svg' width={25} height={25} alt="B4Desk" />
                            </Link>
                        </SheetTitle>
                        <nav className='flex flex-col mt-14'>
                            <Button variant="ghost" className='justify-start gap-1 items-center font-normal' asChild>
                                <Link href="/">
                                    <LayoutDashboard size={24} />
                                    Dashboard
                                </Link>
                            </Button>

                            {(userRole === 'ADMIN' || userRole === 'DEVELOPER') && (
                                <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                    <Link href="/panel">
                                        <PanelTop size={24} />
                                        Painel
                                    </Link>
                                </Button>
                            )}

                            {(['ADMIN', 'DEVELOPER', 'COPY', 'DESIGNER'].includes(userRole)) && (
                                <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                    <Link href="/clients">
                                        <Users size={24} />
                                        Clientes
                                    </Link>
                                </Button>
                            )}

                            {(['COPY', 'ADMIN', 'DEVELOPER', 'DESIGNER'].includes(userRole)) && (
                                <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                    <Link href="/my-demands">
                                        <ListTodo size={24} />
                                        Minhas demandas
                                    </Link>
                                </Button>
                            )}

                            <Separator orientation="horizontal" className='my-2' />

                            <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                <Link href="/notifications">
                                    <Bell size={24} />
                                    Notificações
                                </Link>
                            </Button>

                            <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                <Link href={userRole !== 'VIEWER' ? "/system" : "/updates"}>
                                    <MonitorCog size={24} />
                                    Sistema
                                </Link>
                            </Button>
                        </nav>
                    </SheetContent>
                </Sheet>
                <Link href="/">
                    <div className='flex items-center mx-2 gap-1'>
                        {mounted && (
                            <>
                                <Image
                                    src={
                                        resolvedTheme === "dark"
                                            ? "/logo-b4desk-dark.png"
                                            : "/logo-b4desk-light.png"
                                    }
                                    width={85}
                                    height={32}
                                    alt="B4Desk"
                                    className='hidden xs:block'
                                />
                                <Image
                                    src='/icon-b4desk.svg'
                                    width={25}
                                    height={25}
                                    alt="B4Desk"
                                    className='block xs:hidden'
                                />
                            </>
                        )}
                    </div>
                </Link>
            </div>
            <div className='flex items-center gap-4'>
                <div className='flex gap-2'>
                    <Button variant="outline" size="icon" title='Início' asChild>
                        <Link href="/">
                            <Home />
                        </Link>
                    </Button>
                    <ThemeToggle />
                    <Notifications />
                </div>
                <Sheet>
                    <SheetTrigger title='Minha conta'>
                        <Avatar className='border-2 border-zinc-200'>
                            <AvatarImage src={session?.user?.image ?? 'https://github.com/shadcn.png'} alt={session?.user?.name ?? 'Usuário sem nome'} />
                            <AvatarFallback>
                                {session?.user?.name?.charAt(0).toUpperCase()}
                            </AvatarFallback>
                        </Avatar>
                    </SheetTrigger>
                    <SheetContent side="right">
                        <SheetTitle>
                            Minha conta
                        </SheetTitle>
                        <nav className='flex flex-col gap-2 mt-14'>
                            <Button variant="outline" className='flex justify-start gap-1 items-center font-normal' asChild>
                                <Link href="/profile">
                                    <User size={24} />
                                    Perfil
                                </Link>
                            </Button>
                            <Button variant="outline" className='flex justify-start gap-1 items-center font-normal' asChild>
                                <Link href="/settings">
                                    <Cog size={24} />
                                    Configurações
                                </Link>
                            </Button>
                            <Separator orientation="horizontal" className='my-6' />
                            <AlertDialog>
                                <AlertDialogTrigger asChild>
                                    <Button variant="outline" className='flex justify-start gap-1 items-center font-normal'>
                                        <LogOut />
                                        Sair
                                    </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                    <AlertDialogHeader>
                                        <AlertDialogTitle>
                                            Sair da conta
                                        </AlertDialogTitle>
                                        <AlertDialogDescription>
                                            Deseja realmente sair da sua conta?
                                        </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                        <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                        <AlertDialogAction onClick={handleSignOutClick}>Continuar</AlertDialogAction>
                                    </AlertDialogFooter>
                                </AlertDialogContent>
                            </AlertDialog>
                        </nav>
                    </SheetContent>
                </Sheet>
            </div>
        </header>
    );
};