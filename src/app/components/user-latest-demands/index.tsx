"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { ExternalLink } from "lucide-react";
import { Badge } from "@/app/components/ui/badge";
import { Separator } from "@/app/components/ui/separator";
import { format, isThisMonth, parseISO } from "date-fns";
import { ptBR } from "date-fns/locale";
import { MarkAsCompleted } from "@/app/components/mark-as-completed";
import { ActivityInfo } from "@/app/components/activity-info";
import { ContentAssignmentView } from "@/app/components/content-assignment-view";
import { isPendingDemand } from "@/lib/utils";

interface Client {
    id: string;
    name: string;
    instagramUsername?: string;
}

interface User {
    id: string;
    name: string | null;
    email: string;
    image: string | null;
}

interface Step {
    id: string;
    type: string;
    assignedTo: User;
}

interface WeeklyActivity {
    id: string;
    week: number;
    description: string;
    monthlyPlanning: {
        status: string;
        id: string;
        month: number;
        year: number;
        client: Client;
    };
}

interface Content {
    urlStructuringFeed: string[] | undefined;
    title: string | undefined;
    id: string;
    contentType: string;
    status: string;
    destination: string;
    details?: string;
    activityDate: string;
    reference?: string;
    copywriting?: string;
    caption?: string;
    weeklyActivity?: WeeklyActivity;
    assignedTo?: User | null;
    type?: 'content' | 'general';
    client?: Client;
    isLooseClient?: boolean;
    steps?: Step[];
}

export const UserLatestDemands = () => {
    const [userDemands, setUserDemands] = useState<Content[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const fetchUserDemands = async () => {
            try {
                const response = await fetch('/api/user/demands');

                if (!response.ok) {
                    console.error(`Erro ao buscar demandas do usuário: ${response.status}`);
                    return;
                }

                const data = await response.json();

                const pendingDemands = data.filter((demand: Content) => {
                    if (demand.type === 'content') {
                        return demand.weeklyActivity?.monthlyPlanning?.status === "aprovado" &&
                            isPendingDemand(demand) &&
                            isThisMonth(parseISO(demand.activityDate));
                    }

                    if (demand.type === 'general') {
                        return isPendingDemand(demand) &&
                            isThisMonth(parseISO(demand.activityDate));
                    }

                    return false;
                });

                setUserDemands(pendingDemands.slice(0, 4));
            } catch (error) {
                console.error("Erro ao buscar demandas do usuário:", error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchUserDemands();
    }, []);

    const handleStatusUpdate = async (contentId: string, newStatus: string) => {
        setUserDemands(prevDemands =>
            prevDemands.map(demand =>
                demand.id === contentId
                    ? { ...demand, status: newStatus }
                    : demand
            )
        );

        const demand = userDemands.find(d => d.id === contentId);
        if (!demand) return;

        try {
            const apiUrl = demand.type === 'general'
                ? `/api/general-demands/${contentId}/status`
                : `/api/contents/${contentId}`;

            await fetch(apiUrl, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ status: newStatus }),
            });
        } catch (error) {
            console.error('Erro ao atualizar status:', error);
        }
    };

    if (isLoading) {
        return (
            <div className="mt-4 space-y-4">
                {[...Array(4)].map((_, index) => (
                    <div key={index} className="animate-pulse">
                        <div className="flex items-center gap-2 mb-1">
                            <div className="h-4 bg-gray-200 dark:bg-zinc-700 rounded w-1/4"></div>
                            <div className="h-4 bg-gray-200 dark:bg-zinc-700 rounded w-1/6"></div>
                            <div className="h-4 bg-gray-200 dark:bg-zinc-700 rounded w-1/5"></div>
                        </div>
                        <div className="h-3 bg-gray-200 dark:bg-zinc-700 rounded w-3/4 mb-2"></div>
                        <div className="flex justify-end gap-1">
                            <div className="h-6 w-6 bg-gray-200 dark:bg-zinc-700 rounded"></div>
                            <div className="h-6 w-6 bg-gray-200 dark:bg-zinc-700 rounded"></div>
                            <div className="h-6 w-6 bg-gray-200 dark:bg-zinc-700 rounded"></div>
                        </div>
                    </div>
                ))}
            </div>
        );
    }

    if (userDemands.length === 0) {
        return (
            <p className="flex justify-center items-center my-44 text-sm text-zinc-500">
                Você não possui demandas para o mês atual atribuídas.
            </p>
        );
    }

    return (
        <div className="overflow-auto max-h-[400px]">
            <Separator className="my-2" />
            <p className="text-sm text-zinc-700 dark:text-zinc-100 mb-2">
                Recentes
            </p>
            <ul className="space-y-2">
                {userDemands.map((demand) => (
                    <li key={demand.id} className="text-sm p-3 border rounded-md hover:bg-gray-50 dark:hover:bg-zinc-800">
                        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
                            <div>
                                <div className="flex flex-wrap items-center gap-2 mb-1">
                                    <span className="font-medium">
                                        {demand.type === 'general'
                                            ? demand.client
                                                ? (demand.isLooseClient ? `${demand.client.name}` : demand.client.name)
                                                : "Cliente não especificado"
                                            : demand.weeklyActivity
                                                ? demand.weeklyActivity.monthlyPlanning.client.name
                                                : demand.client
                                                    ? (demand.isLooseClient ? `${demand.client.name}` : demand.client.name)
                                                    : "Cliente não especificado"
                                        }
                                    </span>
                                    <Badge variant="outline" className="text-xs">
                                        {format(new Date(demand.activityDate), "dd/MM/yyyy", { locale: ptBR })}
                                    </Badge>
                                    <Badge variant="outline">
                                        {demand.type === 'general' ? 'demanda pontual' : demand.contentType}
                                    </Badge>
                                </div>
                                <p className="text-xs text-zinc-600 dark:text-zinc-400 line-clamp-2">
                                    {demand.type === 'general'
                                        ? demand.title || demand.details || 'Demanda pontual'
                                        : demand.details || demand.title || `Conteúdo para ${demand.destination || 'destino não especificado'}`
                                    }
                                </p>
                                <ContentAssignmentView
                                    assignedTo={demand.assignedTo}
                                    steps={demand.steps}
                                />
                            </div>
                            <div className="flex flex-wrap justify-end gap-1">
                                <MarkAsCompleted
                                    contentId={demand.id}
                                    currentStatus={demand.status || ''}
                                    onStatusUpdate={(newStatus) => handleStatusUpdate(demand.id, newStatus)}
                                    type={demand.type || 'content'}
                                />
                                <ActivityInfo
                                    activity={{
                                        reference: demand.reference,
                                        copywriting: demand.copywriting,
                                        caption: demand.caption,
                                        url: demand.urlStructuringFeed
                                    }}
                                />
                                <Link href="/my-demands" className="flex items-center">
                                    <ExternalLink size={14} className="text-primary2 flex-shrink-0" />
                                </Link>
                            </div>
                        </div>
                    </li>
                ))}

                {userDemands.length > 0 && (
                    <li className="text-center py-2">
                        <Link
                            href="/my-demands"
                            className="text-xs text-blue-600 hover:underline"
                        >
                            Ver todas as suas demandas
                        </Link>
                    </li>
                )}
            </ul>
        </div>
    );
};
