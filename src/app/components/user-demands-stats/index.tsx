"use client";

import { useEffect, useState } from "react";
import { Card, CardContent } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { CheckCircle, Clock, ListTodo, <PERSON><PERSON>hart } from "lucide-react";
import { isPendingDemand } from "@/lib/utils";

interface Content {
    id: string;
    status?: string;
    dueDate?: string;
    activityDate?: string;
    archived?: boolean;
    type?: string;
    weeklyActivity?: {
        monthlyPlanning?: {
            status?: string;
        };
    };
}

interface DemandsStats {
    total: number;
    completed: number;
    pending: number;
}

export const UserDemandsStats = () => {
    const [stats, setStats] = useState<DemandsStats>({
        total: 0,
        completed: 0,
        pending: 0
    });
    const [isLoading, setIsLoading] = useState(true);
    const [currentMonthName, setCurrentMonthName] = useState<string>("");

    useEffect(() => {
        const date = new Date();
        const monthNames = [
            'Janeiro', 'Fever<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Agos<PERSON>',
            'Setem<PERSON>', 'Outubro', 'Novembro', 'Dezembro'
        ];
        setCurrentMonthName(monthNames[date.getMonth()]);
    }, []);

    useEffect(() => {
        const fetchDemandsStats = async () => {
            try {
                const response = await fetch('/api/user/demands');

                if (!response.ok) {
                    console.error(`Erro ao buscar estatísticas de demandas: ${response.status}`);
                    return;
                }

                const allDemands: Content[] = await response.json();

                const currentDate = new Date();
                const currentMonth = currentDate.getMonth();
                const currentYear = currentDate.getFullYear();

                const currentMonthDemands = allDemands.filter((demand: Content) => {
                    if (demand.archived) {
                        return false;
                    }

                    const demandDate = new Date(demand.activityDate || '');
                    if (!demandDate || isNaN(demandDate.getTime())) {
                        return false;
                    }

                    const isCurrentMonth = demandDate.getMonth() === currentMonth &&
                        demandDate.getFullYear() === currentYear;

                    return isCurrentMonth;
                });

                const filteredDemands = currentMonthDemands.filter(demand => {
                    if (demand.type === 'general' || !demand.weeklyActivity?.monthlyPlanning) {
                        return true;
                    }

                    return demand.weeklyActivity.monthlyPlanning.status === 'aprovado';
                });

                const completedStatuses = ['concluído', 'captado', 'pend. captação', 'em revisão', 'anúncio concluído'];

                const completedDemands = filteredDemands.filter(
                    (demand: Content) => completedStatuses.includes(demand.status || '')
                );

                const pendingDemands = filteredDemands.filter(
                    (demand: Content) => {
                        const isPending = isPendingDemand(demand);

                        return isPending;
                    }
                );

                setStats({
                    total: filteredDemands.length,
                    completed: completedDemands.length,
                    pending: pendingDemands.length
                });
            } catch (error) {
                console.error("Erro ao buscar estatísticas de demandas:", error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchDemandsStats();
    }, []);

    const completionPercentage = stats.total > 0
        ? Math.round((stats.completed / stats.total) * 100)
        : 0;

    if (isLoading) {
        return (
            <div className="space-y-4 mt-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {[...Array(3)].map((_, index) => (
                        <Card key={index}>
                            <CardContent className="p-4 flex items-center gap-3">
                                <div className="bg-gray-200 dark:bg-zinc-700 w-10 h-10 rounded-full animate-pulse" />
                                <div className="flex-1">
                                    <div className="h-4 bg-gray-200 dark:bg-zinc-700 rounded w-3/4 mb-2 animate-pulse" />
                                    <div className="h-6 bg-gray-200 dark:bg-zinc-700 rounded w-1/2 animate-pulse" />
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                            <div className="h-4 bg-gray-200 dark:bg-zinc-700 rounded w-1/4 animate-pulse" />
                            <div className="h-6 bg-gray-200 dark:bg-zinc-700 rounded w-16 animate-pulse" />
                        </div>
                        <div className="w-full bg-gray-200 dark:bg-zinc-700 h-2 rounded-full overflow-hidden animate-pulse" />
                        <div className="h-4 bg-gray-200 dark:bg-zinc-700 rounded w-1/2 mt-2 animate-pulse" />
                    </CardContent>
                </Card>
            </div>
        );
    }

    if (stats.total === 0) {
        return (
            <div className="flex justify-center items-center h-40">
                <p className="text-sm text-zinc-500">Você não possui demandas para {currentMonthName}.</p>
            </div>
        );
    }

    return (
        <div className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                    <CardContent className="p-4 flex items-center gap-3">
                        <div className="bg-blue-100 dark:bg-blue-900 p-2 rounded-full">
                            <ListTodo size={20} className="text-blue-600 dark:text-blue-300" />
                        </div>
                        <div>
                            <p className="text-xs text-zinc-500 dark:text-zinc-400">Demandas de {currentMonthName}</p>
                            <p className="text-2xl font-bold">{stats.total}</p>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4 flex items-center gap-3">
                        <div className="bg-green-100 dark:bg-green-900 p-2 rounded-full">
                            <CheckCircle size={20} className="text-green-600 dark:text-green-300" />
                        </div>
                        <div>
                            <p className="text-xs text-zinc-500 dark:text-zinc-400">Concluídas</p>
                            <p className="text-2xl font-bold">{stats.completed}</p>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4 flex items-center gap-3">
                        <div className="bg-amber-100 dark:bg-amber-900 p-2 rounded-full">
                            <Clock size={20} className="text-amber-600 dark:text-amber-300" />
                        </div>
                        <div>
                            <p className="text-xs text-zinc-500 dark:text-zinc-400">Pendentes</p>
                            <p className="text-2xl font-bold">{stats.pending}</p>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <Card>
                <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                            <PieChart size={18} className="text-primary2" />
                            <p className="text-sm font-medium">Progresso</p>
                        </div>
                        <Badge variant="outline">{completionPercentage}%</Badge>
                    </div>
                    <div className="w-full bg-secondary h-2 rounded-full overflow-hidden">
                        <div
                            className="bg-primary h-full transition-all"
                            style={{ width: `${completionPercentage}%` }}
                        />
                    </div>
                    <p className="text-xs text-zinc-500 dark:text-zinc-400 mt-2">
                        {stats.completed} de {stats.total} demandas concluídas em {currentMonthName}
                    </p>
                </CardContent>
            </Card>
        </div>
    );
};
