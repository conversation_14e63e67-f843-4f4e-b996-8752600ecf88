"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { ExternalLink } from "lucide-react";
import { Badge } from "@/app/components/ui/badge";
import { Separator } from "../ui/separator";

type ContentWithClient = {
    id: string;
    contentType: string;
    status: string;
    destination: string;
    details?: string;
    activityDate: string;
    clientId: string;
    clientName: string;
    week: number;
    month: number;
    year: number;
};

export const FeedStructuringTasks = () => {
    const [pendingContents, setPendingContents] = useState<ContentWithClient[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const fetchPendingContents = async () => {
            try {
                const response = await fetch('/api/feed-structuring-tasks');
                
                if (!response.ok) {
                    console.error(`Erro ao buscar conteúdos de estruturação de Feed: ${response.status}`);
                    return;
                }

                const data = await response.json() as ContentWithClient[];
                setPendingContents(data);
            } catch (error) {
                console.error("Erro ao buscar conteúdos pendentes:", error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchPendingContents();
    }, []); 

    if (isLoading) {
        return (
            <div className="animate-pulse mt-4 space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-zinc-700 rounded w-3/4"></div>
                <div className="h-10 bg-gray-200 dark:bg-zinc-700 rounded"></div>
                <div className="h-10 bg-gray-200 dark:bg-zinc-700 rounded"></div>
                <div className="h-10 bg-gray-200 dark:bg-zinc-700 rounded"></div>
                <div className="h-10 bg-gray-200 dark:bg-zinc-700 rounded"></div>
            </div>
        );
    }

    if (pendingContents.length === 0) {
        return (
            <p className="flex justify-center items-center my-44 text-sm text-zinc-500">
                Não há demandas de estruturação de Feed pendentes no momento.
            </p>
        );
    }

    return (
        <div className="overflow-auto max-h-[400px]">
            <Separator className="my-2" />
            <div className="flex items-center gap-2 mb-2">
                <p className="text-sm text-zinc-700 dark:text-zinc-100">
                    Conteúdos pendentes de estruturação do Feed
                </p>
                <Badge variant="outline" className="font-bold">{pendingContents.length}</Badge>
            </div>
            <ul className="space-y-2">
                {pendingContents.slice(0, 4).map((content) => (
                    <li key={content.id} className="text-sm p-2 border rounded-md hover:bg-gray-50 dark:hover:bg-zinc-800">
                        <Link href={`/feed-structuring/${content.clientId}`} className="block">
                            <div className="flex items-start justify-between">
                                <div>
                                    <div className="flex items-center gap-2 mb-1">
                                        <span className="font-medium">{content.clientName}</span>
                                        <Badge variant="outline" className="text-xs">
                                            {new Date(content.activityDate).toLocaleDateString('pt-BR')}
                                        </Badge>
                                        <Badge variant="outline" >{content.contentType}</Badge>
                                    </div>
                                    <p className="text-xs text-zinc-600 dark:text-zinc-400 line-clamp-2">
                                        {content.details || `Conteúdo para ${content.destination}`}
                                    </p>
                                </div>
                                <ExternalLink size={16} className="text-primary2 flex-shrink-0 mt-1" />
                            </div>
                        </Link>
                    </li>
                ))}

                {pendingContents.length > 4 && (
                    <li className="text-center py-2">
                        <Link
                        href={`/feed-structuring/${pendingContents[0].clientId}`}
                            className="text-xs text-blue-600 hover:underline"
                        >
                            Ver todos os {pendingContents.length} conteúdos pendentes
                        </Link>
                    </li>
                )}
            </ul>
        </div>
    );
};