"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/app/components/ui/select";
import { toast } from "sonner";
import { Edit, Ellipsis, Plus, Trash2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/app/components/ui/dialog";
import Link from "next/link";

interface AddFeedUrlModalProps {
  contentId: string;
  currentUrl?: string | string[];
  currentUrlTypes?: string[];
  currentMediaTypes?: string[];
  currentUrlFolder?: string;
  onUrlUpdated: (contentId: string, newUrls: string[], newUrlTypes: string[], newMediaTypes?: string[], newUrlFolder?: string) => void;
  type?: 'content' | 'general';
}

interface UrlItem {
  id: string;
  url: string;
  type: 'feed' | 'story';
  mediaType: 'foto' | 'video';
}

const extractGoogleDriveId = (url: string): string | null => {
  if (!url) return null;

  const match1 = url.match(/\/d\/([^/]+)/);
  if (match1) return match1[1];

  const match2 = url.match(/id=([^&]+)/);
  if (match2) return match2[1];

  return null;
};

export const AddFeedUrlModal = ({ contentId, currentUrl, currentUrlTypes, currentMediaTypes, currentUrlFolder, onUrlUpdated, type = 'content' }: AddFeedUrlModalProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [urls, setUrls] = useState<UrlItem[]>([]);
  const [urlFolder, setUrlFolder] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [selectedUrlIndex, setSelectedUrlIndex] = useState<number>(0);
  const [hasInitialized, setHasInitialized] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  useEffect(() => {
    const checkForUnsavedChanges = () => {
      if (!hasInitialized) return false;

      const currentUrls = urls.map(item => item.url);
      const currentTypes = urls.map(item => item.type);
      const currentMediaTypes = urls.map(item => item.mediaType);

      const originalUrls = Array.isArray(currentUrl) ? currentUrl : (currentUrl ? [currentUrl] : []);
      const originalTypes = currentUrlTypes || [];
      const originalMediaTypes = currentMediaTypes || Array(originalUrls.length).fill('foto');

      const urlsChanged = JSON.stringify(currentUrls) !== JSON.stringify(originalUrls);
      const typesChanged = JSON.stringify(currentTypes) !== JSON.stringify(originalTypes);
      const mediaTypesChanged = JSON.stringify(currentMediaTypes) !== JSON.stringify(originalMediaTypes);

      return urlsChanged || typesChanged || mediaTypesChanged;
    };

    setHasUnsavedChanges(checkForUnsavedChanges());
  }, [urls, hasInitialized, currentUrl, currentUrlTypes, currentMediaTypes]);

  const handleCloseModal = () => {
    if (hasUnsavedChanges) {
      if (confirm('Você tem alterações não salvas. Deseja realmente fechar sem salvar?')) {
        setIsOpen(false);
      }
    } else {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    if (isOpen && !hasInitialized) {
      if (currentUrl) {
        if (Array.isArray(currentUrl)) {
          const urlItems = currentUrl.map((url, index) => ({
            id: `url-${index}`,
            url,
            type: (currentUrlTypes?.[index] === 'story' ? 'story' : 'feed') as 'feed' | 'story',
            mediaType: (currentMediaTypes?.[index] === 'video' ? 'video' : 'foto') as 'foto' | 'video'
          }));
          setUrls(urlItems);
        } else {
          setUrls([{
            id: 'url-0',
            url: currentUrl,
            type: (currentUrlTypes?.[0] === 'story' ? 'story' : 'feed'),
            mediaType: (currentMediaTypes?.[0] === 'video' ? 'video' : 'foto')
          }]);
        }
      } else {
        setUrls([{ id: 'url-0', url: '', type: 'feed', mediaType: 'foto' }]);
      }

      // Inicializar urlFolder com o valor atual
      setUrlFolder(currentUrlFolder || null);

      setHasInitialized(true);
    }
  }, [isOpen, currentUrl, currentUrlTypes, currentMediaTypes, currentUrlFolder, hasInitialized]);

  useEffect(() => {
    if (!isOpen) {
      setHasInitialized(false);
    }
  }, [isOpen]);

  const handleOpenDialog = () => {
    setSelectedUrlIndex(0);
    setIsOpen(true);
  };

  const handleUpdateUrl = async () => {
    if (!contentId) return;

    setIsUpdating(true);

    try {
      const validUrls = urls.filter(item => item.url.trim() !== '');

      for (const urlItem of validUrls) {
        if (urlItem.url && !urlItem.url.includes('drive.google.com')) {
          toast.error("Por favor, insira apenas URLs válidas do Google Drive");
          setIsUpdating(false);
          return;
        }
      }

      const apiUrl = type === 'general'
        ? `/api/general-demands/${contentId}/url-structuring`
        : `/api/contents/${contentId}/url-structuring`;

      const urlsToSave = validUrls.map(item => item.url);
      const urlsWithTypes = validUrls.map(item => ({
        url: item.url,
        type: item.type,
        mediaType: item.mediaType
      }));

      const response = await fetch(apiUrl, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          urlStructuringFeed: urlsToSave,
          urlsWithTypes: urlsWithTypes,
          urlFolder: urlFolder
        }),
      });

      if (!response.ok) {
        throw new Error(`Erro ao atualizar URLs: ${response.status}`);
      }

      const urlTypesToSave = validUrls.map(item => item.type);
      const mediaTypesToSave = validUrls.map(item => item.mediaType);

      toast.success("URLs atualizadas com sucesso");
      onUrlUpdated(contentId, urlsToSave, urlTypesToSave, mediaTypesToSave, urlFolder || undefined);
      setHasUnsavedChanges(false);
      setIsOpen(false);
    } catch (error) {
      console.error("Erro ao atualizar URLs:", error);
      toast.error("Erro ao atualizar URLs");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleUrlChange = (index: number, newUrl: string) => {
    const newUrls = [...urls];
    newUrls[index].url = newUrl;
    setUrls(newUrls);
  };

  const handleTypeChange = (index: number, newType: 'feed' | 'story') => {
    const newUrls = [...urls];
    newUrls[index].type = newType;
    setUrls(newUrls);
  };

  const handleMediaTypeChange = (index: number, newMediaType: 'foto' | 'video') => {
    const newUrls = [...urls];
    newUrls[index].mediaType = newMediaType;
    setUrls(newUrls);
  };

  const addNewUrl = () => {
    const newId = `url-${Date.now()}`;
    setUrls([...urls, { id: newId, url: '', type: 'feed', mediaType: 'foto' }]);
    setSelectedUrlIndex(urls.length);
  };

  const removeUrl = (index: number) => {
    if (urls.length <= 1) {
      toast.error("Deve haver pelo menos uma URL");
      return;
    }

    const newUrls = urls.filter((_, i) => i !== index);
    setUrls(newUrls);

    if (selectedUrlIndex >= newUrls.length) {
      setSelectedUrlIndex(newUrls.length - 1);
    } else if (selectedUrlIndex > index) {
      setSelectedUrlIndex(selectedUrlIndex - 1);
    }
  };

  const getCurrentDriveId = () => {
    if (urls[selectedUrlIndex]?.url) {
      return extractGoogleDriveId(urls[selectedUrlIndex].url);
    }
    return null;
  };

  const getCurrentUrl = () => {
    return urls[selectedUrlIndex]?.url || '';
  };

  return (
    <>
      <Button
        variant="outline"
        size="icon"
        onClick={handleOpenDialog}
        title="Editar URL do Google Drive"
      >
        <Edit size={14} />
      </Button>

      <Dialog open={isOpen} onOpenChange={handleCloseModal}>
        <DialogContent className="max-w-md overflow-y-auto max-h-[90vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {currentUrl ? "Editar URLs" : "Adicionar URLs"}
              {hasUnsavedChanges && (
                <span className="text-xs bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded">
                  Não salvo
                </span>
              )}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <div className="my-2">
                <Label className="text-sm font-medium">
                  Pasta no Google Drive
                </Label>
                <p className="text-xs text-muted-foreground mt-1">
                  Selecione a pasta no Google Drive que contém todas as imagens e vídeos desse conteúdo. Isso facilitará a organização e compartilhamento.
                </p>
                <Input
                  value={urlFolder || ''}
                  onChange={(e) => setUrlFolder(e.target.value)}
                  placeholder="https://drive.google.com/drive/folders/..."
                  className="mt-1.5"
                />
              </div>

              <Label className="text-sm font-medium">
                URLs do Google Drive
              </Label>

              <p className="text-xs text-muted-foreground mt-1">
                Cada URL representa um criativo diferente. Clique na URL para selecioná-la e ver sua visualização. Configure o formato (Feed/Story) e o tipo de mídia (Foto/Vídeo).
              </p>

              <div className="space-y-3 mt-2">
                {urls.map((urlItem, index) => (
                  <div key={urlItem.id} className="border rounded-lg p-3 space-y-2">
                    <div className="flex gap-2 items-center">
                      <div className="flex-1">
                        <Input
                          value={urlItem.url}
                          onChange={(e) => handleUrlChange(index, e.target.value)}
                          placeholder="https://drive.google.com/file/d/..."
                          className={`${selectedUrlIndex === index ? 'ring-2 ring-blue-500' : ''}`}
                          onFocus={() => setSelectedUrlIndex(index)}
                        />
                      </div>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => removeUrl(index)}
                        disabled={urls.length <= 1}
                        className="h-10 w-10"
                        title="Remover URL"
                      >
                        <Trash2 size={14} />
                      </Button>
                    </div>
                    <div className="flex gap-2 items-center">
                      <div className="flex flex-col gap-2 flex-1">
                        <div className="flex gap-2 items-center">
                          <Label className="text-xs text-muted-foreground min-w-fit">
                            Formato:
                          </Label>
                          <Select
                            value={urlItem.type}
                            onValueChange={(value: 'feed' | 'story') => handleTypeChange(index, value)}
                          >
                            <SelectTrigger className="w-32 h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="feed">Feed</SelectItem>
                              <SelectItem value="story">Story</SelectItem>
                            </SelectContent>
                          </Select>
                          <div className="text-xs text-muted-foreground">
                            {urlItem.type === 'feed' ? '📱 Para o feed' : '📖 Para stories'}
                          </div>
                        </div>
                        <div className="flex gap-2 items-center">
                          <Label className="text-xs text-muted-foreground min-w-fit">
                            Tipo:
                          </Label>
                          <Select
                            value={urlItem.mediaType}
                            onValueChange={(value: 'foto' | 'video') => handleMediaTypeChange(index, value)}
                          >
                            <SelectTrigger className="w-32 h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="foto">Foto</SelectItem>
                              <SelectItem value="video">Vídeo</SelectItem>
                            </SelectContent>
                          </Select>
                          <div className="text-xs text-muted-foreground">
                            {urlItem.mediaType === 'foto' ? '🖼️ Imagem estática' : '🎥 Conteúdo em vídeo'}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <Button
                variant="outline"
                onClick={addNewUrl}
                className="mt-2 w-full"
                size="sm"
              >
                <Plus size={16} />
                Adicionar URL
              </Button>

              <p className="text-xs text-muted-foreground mt-2">
                Cole as URLs do Google Drive e configure o formato e tipo de mídia. A URL selecionada será exibida na visualização abaixo.
              </p>
            </div>

            <div className="mt-4">
              <Label className="text-sm font-medium mb-2 block">
                Visualização - Criativo {selectedUrlIndex + 1} ({urls[selectedUrlIndex]?.type === 'feed' ? 'Feed' : 'Story'} - {urls[selectedUrlIndex]?.mediaType === 'foto' ? 'Foto' : 'Vídeo'})
              </Label>
              <div className="border rounded-md p-3">
                {getCurrentDriveId() ? (
                  <div className="flex flex-col gap-2">
                    <div className="h-[400px] flex items-center justify-center bg-gray-100 rounded relative">
                      <Image
                        src={`/api/drive-proxy?id=${getCurrentDriveId()}&quality=high&size=large`}
                        alt="Visualização do feed"
                        width={500}
                        height={625}
                        className="max-w-full max-h-full object-contain"
                        style={{
                          width: 'auto',
                          height: 'auto',
                          maxWidth: '100%',
                          maxHeight: '100%'
                        }}
                      />
                    </div>
                    <Link href={getCurrentUrl()} target="_blank" rel="noopener noreferrer" className="text-xs text-blue-500 hover:underline flex items-center gap-1">
                      <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width={12} height={12} fill="oklch(62.3% 0.214 259.815)"><title>Google Drive</title><path d="M12.01 1.485c-2.082 0-3.754.02-3.743.047.01.02 1.708 3.001 3.774 6.62l3.76 6.574h3.76c2.081 0 3.753-.02 3.742-.047-.005-.02-1.708-3.001-3.775-6.62l-3.76-6.574zm-4.76 1.73a789.828 789.861 0 0 0-3.63 6.319L0 15.868l1.89 3.298 1.885 3.297 3.62-6.335 3.618-6.33-1.88-3.287C8.1 4.704 7.255 3.22 7.25 3.214zm2.259 12.653-.203.348c-.114.198-.96 1.672-1.88 3.287a423.93 423.948 0 0 1-1.698 2.97c-.01.026 3.24.042 7.222.042h7.244l1.796-3.157c.992-1.734 1.85-3.23 1.906-3.323l.104-.167h-7.249z" /></svg>
                      Ver no Google Drive
                    </Link>
                  </div>
                ) : (
                  <div className="h-[400px] flex items-center justify-center bg-gray-50 text-gray-400 dark:bg-zinc-800 dark:text-zinc-500 text-sm">
                    {getCurrentUrl() ? "Não foi possível carregar a visualização" : "Insira uma URL para visualizar a imagem"}
                  </div>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="secondary"
              onClick={handleCloseModal}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleUpdateUrl}
              disabled={isUpdating}
              className="mb-2 sm:mb-0"
            >
              {isUpdating ? (
                <>
                  <Ellipsis />
                </>
              ) : (
                <>
                  Salvar URLs
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
