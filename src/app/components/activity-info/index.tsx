import { ClipboardCopy, Eye, Info } from "lucide-react";
import { Button } from "../ui/button";
import { Dialog, DialogContent, DialogTitle, DialogHeader, DialogTrigger } from "../ui/dialog";
import { toast } from "sonner";

interface Activity {
    copywriting?: string;
    caption?: string;
    reference?: string;
    url?: string[];
    urlFolder?: string;
}

interface ActivityInfoProps {
    activity: Activity;
    localReference?: string;
    localCopywriting?: string;
    localCaption?: string;
    localURL?: string[];
    localUrlFolder?: string;
}

export const ActivityInfo = ({ activity, localReference, localCopywriting, localCaption, localURL, localUrlFolder }: ActivityInfoProps) => {
    const hasContent = () => {
        const hasCaption = !!(localCaption?.trim() || activity.caption?.trim());
        
        const hasCopywriting = !!(localCopywriting?.trim() || activity.copywriting?.trim());
        
        const hasReference = !!(localReference?.trim() || activity.reference?.trim());
        
        const urls = localURL || activity.url || [];
        const urlArray = Array.isArray(urls) ? urls : [urls];
        const hasUrls = urlArray.some(url => url && typeof url === 'string' && url.trim() !== '');
        const hasUrlFolder = !!(localUrlFolder?.trim() || activity.urlFolder?.trim());
        
        return hasCaption || hasCopywriting || hasReference || hasUrls || hasUrlFolder;
    };

    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button variant="outline" size="icon" title="Ver mais informações"
                    className={`${hasContent() ? "text-primary2 bg-orange-200 dark:bg-orange-950 hover:bg-orange-200 hover:text-black dark:hover:text-white" : "text-gray-400"
                        }`}
                >
                    <Eye />
                </Button>
            </DialogTrigger>
            <DialogContent className="max-h-[95vh] w-full max-w-[95vw] sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle className="px-2 text-sm flex items-center gap-1">
                        <div className="relative">
                            <Info size={16} />
                        </div>
                        Informações
                    </DialogTitle>
                </DialogHeader>
                <div className="overflow-y-auto px-2 max-h-[calc(95vh-110px)] pb-4">
                    {hasContent() ? (
                        <>
                            <div className="flex flex-col">
                                <div className="italic text-sm border-t border-dashed border-zinc-200 bg-zinc-50 dark:bg-zinc-800 p-2 mb-2">
                                    <strong className="text-zinc-700 dark:text-zinc-400">Legenda: </strong>
                                    {(localCaption || activity.caption) ? (
                                        <div className="whitespace-pre-wrap break-words">{localCaption || activity.caption}</div>
                                    ) : (
                                        <p className="not-italic">Nenhuma legenda cadastrada</p>
                                    )}
                                </div>
                                <Button
                                    variant="outline"
                                    className="text-xs sm:text-sm"
                                    onClick={() => {
                                        navigator.clipboard.writeText(localCaption || activity.caption || "");
                                        toast.success("Legenda copiada para a área de transferência");
                                    }}
                                >
                                    <ClipboardCopy />
                                    Copiar legenda
                                </Button>
                            </div>

                            <div className="flex flex-col mt-4">
                                <div className="italic text-sm border-t border-dashed border-zinc-200 bg-zinc-50 dark:bg-zinc-800 p-2 mb-2">
                                    <strong className="text-zinc-700 dark:text-zinc-400">Referência: </strong>
                                    {(localReference || activity.reference) ? (
                                        <div className="whitespace-pre-wrap break-words">
                                            {(() => {
                                                const reference = localReference || activity.reference;
                                                return reference?.startsWith('http') ? (
                                                    <a
                                                        href={reference}
                                                        target="_blank"
                                                        rel="noopener noreferrer"
                                                        className="text-blue-500 hover:underline"
                                                    >
                                                        <span>{reference}</span>
                                                    </a>
                                                ) : (
                                                    reference
                                                );
                                            })()
                                            }
                                        </div>
                                    ) : (
                                        <p className="not-italic">Nenhuma referência cadastrada</p>
                                    )}
                                </div>
                                <Button
                                    variant="outline"
                                    className="text-xs sm:text-sm"
                                    onClick={() => {
                                        navigator.clipboard.writeText(localReference || activity.reference || "");
                                        toast.success("URL copiada para a área de transferência");
                                    }}
                                >
                                    <ClipboardCopy />
                                    Copiar referência
                                </Button>
                            </div>

                            <div className="flex flex-col mt-4">
                                <div className="italic text-sm border-t border-dashed border-zinc-200 bg-zinc-50 dark:bg-zinc-800 p-2 mb-2">
                                    <strong className="text-zinc-700 dark:text-zinc-400">Copywriting: </strong>
                                    {(localCopywriting || activity.copywriting) ? (
                                        <div className="whitespace-pre-wrap break-words">{localCopywriting || activity.copywriting}</div>
                                    ) : (
                                        <p className="not-italic">Nenhum copywriting cadastrado</p>
                                    )}
                                </div>
                                <Button
                                    variant="outline"
                                    className="text-xs sm:text-sm"
                                    onClick={() => {
                                        navigator.clipboard.writeText(localCopywriting || activity.copywriting || "");
                                        toast.success("Copywriting copiado para a área de transferência");
                                    }}
                                >
                                    <ClipboardCopy />
                                    Copiar copywriting
                                </Button>
                            </div>

                            <div className="flex flex-col mt-4">
                                <div className="italic text-sm border-t border-dashed border-zinc-200 bg-zinc-50 dark:bg-zinc-800 p-2 mb-2">
                                    <strong className="text-zinc-700 dark:text-zinc-400">URLs: </strong>
                                    {(() => {
                                        const urls = localURL ? localURL : (Array.isArray(activity.url) ? activity.url : activity.url ? [activity.url] : []);
                                        const validUrls = urls.filter(url => url && typeof url === 'string' && url.trim() !== '');
                                        
                                        return validUrls.length > 0 ? (
                                            <div className="whitespace-pre-wrap break-words">
                                                {validUrls.map((url, index) => (
                                                    <div key={index} className="mt-1">
                                                        <span className="text-xs text-gray-600 dark:text-gray-400">Criativo {index + 1}: </span>
                                                        <a
                                                            href={url}
                                                            target="_blank"
                                                            rel="noopener noreferrer"
                                                            className="text-blue-500 hover:underline break-all"
                                                        >
                                                            {url}
                                                        </a>
                                                    </div>
                                                ))}
                                            </div>
                                        ) : (
                                            <p className="not-italic">Nenhuma URL cadastrada</p>
                                        );
                                    })()}
                                </div>
                                <Button
                                    variant="outline"
                                    className="text-xs sm:text-sm"
                                    onClick={() => {
                                        const urls = localURL ? localURL : (Array.isArray(activity.url) ? activity.url : activity.url ? [activity.url] : []);
                                        const validUrls = urls.filter(url => url && typeof url === 'string' && url.trim() !== '');
                                        const urlsText = validUrls.join('\n');
                                        navigator.clipboard.writeText(urlsText);
                                        toast.success(`${validUrls.length} URL${validUrls.length > 1 ? 's' : ''} copiada${validUrls.length > 1 ? 's' : ''} para a área de transferência`);
                                    }}
                                >
                                    <ClipboardCopy />
                                    Copiar URLs
                                </Button>
                            </div>

                            <div className="flex flex-col mt-4">
                                <div className="italic text-sm border-t border-dashed border-zinc-200 bg-zinc-50 dark:bg-zinc-800 p-2 mb-2">
                                    <strong className="text-zinc-700 dark:text-zinc-400">Pasta no Google Drive: </strong>
                                    {(localUrlFolder || activity.urlFolder) ? (
                                        <div className="whitespace-pre-wrap break-words">{localUrlFolder || activity.urlFolder}</div>
                                    ) : (
                                        <p className="not-italic">Nenhuma pasta no Google Drive cadastrada</p>
                                    )}
                                </div>
                                <Button
                                    variant="outline"
                                    className="text-xs sm:text-sm"
                                    onClick={() => {
                                        navigator.clipboard.writeText(localUrlFolder || activity.urlFolder || "");
                                        toast.success("Pasta no Google Drive copiada para a área de transferência");
                                    }}
                                >
                                    <ClipboardCopy />
                                    Copiar URL da pasta no Google Drive
                                </Button>
                            </div>
                        </>
                    ) : (
                        <p className="mt-2">
                            <span className="text-gray-500">Nenhuma informação</span>
                        </p>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    );
}