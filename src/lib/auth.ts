/* eslint-disable @typescript-eslint/no-explicit-any */
import prisma from "@/app/lib/prisma";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import { compare } from "bcryptjs";

export const authOptions = {
    debug: process.env.NODE_ENV !== 'production',
    pages: { signIn: '/login' },
    session: {
        strategy: "jwt" as const
    },
    providers: [
        CredentialsProvider({
            name: "<PERSON>ail e Senha",
            credentials: {
                email: { label: "Email", type: "email" },
                password: { label: "<PERSON><PERSON>", type: "password" }
            },
            async authorize(credentials) {
                if (!credentials?.email || !credentials.password) {
                    throw new Error('Email e senha são obrigatórios');
                }

                const user = await prisma.user.findUnique({
                    where: { email: credentials.email }
                });

                if (!user) {
                    throw new Error('Usu<PERSON>rio não encontrado');
                }

                const { hashedPassword } = user as any;
                if (!hashedPassword) {
                    throw new Error('Usuário não possui senha configurada');
                }

                const isValid = await compare(credentials.password, hashedPassword);
                if (!isValid) {
                    throw new Error('Senha incorreta');
                }

                return {
                    id: user.id,
                    email: user.email,
                    name: user.name,
                    role: user.role,
                    accessLevel: user.accessLevel
                } as any;
            }
        }),
        GoogleProvider({
            clientId: process.env.GOOGLE_CLIENT_ID as string,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
        })
    ],
    secret: process.env.NEXT_AUTH_SECRET,
    callbacks: {
        async signIn({ user, account }: any) {
            if (account?.provider === "google" && user?.email) {
                try {
                    const existingUser = await prisma.user.findUnique({
                        where: { email: user.email },
                        include: {
                            accounts: {
                                where: { provider: "google" }
                            }
                        }
                    });

                    if (!existingUser) {
                        await prisma.user.create({
                            data: {
                                email: user.email,
                                name: user.name,
                                image: user.image,
                                role: "VIEWER",
                                accessLevel: "VIEWER",
                                accounts: {
                                    create: {
                                        type: account.type,
                                        provider: account.provider,
                                        providerAccountId: account.providerAccountId,
                                        access_token: account.access_token,
                                        id_token: account.id_token,
                                        refresh_token: account.refresh_token,
                                        token_type: account.token_type,
                                        scope: account.scope,
                                        expires_at: account.expires_at,
                                        session_state: account.session_state
                                    }
                                }
                            }
                        });

                    } else if (existingUser.accounts.length === 0) {
                        await prisma.account.create({
                            data: {
                                userId: existingUser.id,
                                type: account.type,
                                provider: account.provider,
                                providerAccountId: account.providerAccountId,
                                access_token: account.access_token,
                                id_token: account.id_token,
                                refresh_token: account.refresh_token,
                                token_type: account.token_type,
                                scope: account.scope,
                                expires_at: account.expires_at,
                                session_state: account.session_state
                            }
                        });
                    }
                } catch (error) {
                    console.error("Erro ao processar login com Google:", error);
                }
            }

            return true;
        },

        async jwt({ token, user }: any) {
            if (user) {
                token.id = user.id;
                token.role = user.role;
                token.accessLevel = user.accessLevel;
            }
            return token;
        },

        async session({ session, token }: any) {
            if (session.user) {
                try {
                    const dbUser = await prisma.user.findUnique({
                        where: { email: session.user.email ?? undefined },
                        select: {
                            id: true,
                            name: true,
                            role: true,
                            accessLevel: true,
                            image: true
                        }
                    });

                    session.user.id = dbUser?.id;
                    session.user.name = dbUser?.name ?? session.user.name;
                    session.user.image = dbUser?.image ?? session.user.image;
                    session.user.role = dbUser?.role ?? token.role ?? "VIEWER";
                    session.user.accessLevel = dbUser?.accessLevel ?? token.accessLevel ?? "VIEWER";
                } catch (error) {
                    console.error("Erro ao buscar usuário:", error);
                    session.user.role = token.role ?? "VIEWER";
                    session.user.accessLevel = token.accessLevel ?? "VIEWER";
                }
            }
            return session;
        }
    }
};