if(!self.define){let e,s={};const a=(a,n)=>(a=new URL(a+".js",n).href,s[a]||new Promise((s=>{if("document"in self){const e=document.createElement("script");e.src=a,e.onload=s,document.head.appendChild(e)}else e=a,importScripts(a),s()})).then((()=>{let e=s[a];if(!e)throw new Error(`Module ${a} didn’t register its module`);return e})));self.define=(n,i)=>{const t=e||("document"in self?document.currentScript.src:"")||location.href;if(s[t])return;let c={};const p=e=>a(e,t),r={module:{uri:t},exports:c,require:p};s[t]=Promise.all(n.map((e=>r[e]||p(e)))).then((e=>(i(...e),c)))}}define(["./workbox-e9849328"],(function(e){"use strict";importScripts("fallback-RNeGBXunsppNvWa8bUg7S.js"),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"5140a0ad483dff9fb15c65f476d55a48"},{url:"/_next/static/RNeGBXunsppNvWa8bUg7S/_buildManifest.js",revision:"2a9de55b2a7b69f7c7f438697113ae8e"},{url:"/_next/static/RNeGBXunsppNvWa8bUg7S/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/1306-ccb92b4701acdb50.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/1390-5e0061eaac1c4642.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/1858-b078bf85c0a9b6f0.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/1986-fe2dc41436dbb0e9.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/2423-4097e4e6e3595144.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/2587-1df14521bdcd1395.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/3074-4b59a94acc8b31ab.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/3488-0ad1a457d51df898.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/3642-0c97911e8577a00d.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/3696-559d8032aba40ded.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/4009-f27bf977f96d4e7a.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/4072-c135ba754dc1d5dc.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/4214-403014c49d1d59d3.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/4312.b6e8baa641babe94.js",revision:"b6e8baa641babe94"},{url:"/_next/static/chunks/4365-3435bf37716f5797.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/4585-a390a090f43c3f0e.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/4669.cc0b1550fa47883c.js",revision:"cc0b1550fa47883c"},{url:"/_next/static/chunks/491-6210ec3dbc19cf4e.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/5353-6f117afd072bd615.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/5899e4d3-612fa25337322ed3.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/6566-604be39170a6b2f0.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/6709-b5a5231a308b02f7.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/6958-ae650a097a777496.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/7190-30185bd09c057ae1.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/7382-38f8509762f408f5.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/7573-4ac1b65638cbb554.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/7708.5181300675315f33.js",revision:"5181300675315f33"},{url:"/_next/static/chunks/7833-5d2d8d86cdaf7b28.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/7984-82565dd0803f0be4.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/7a38de25-f42777e8a53adeb7.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/812-9f3697e2e32735db.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/8161-927ad218f33cb569.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/8294-a0f73f2cc740994a.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/8581-31af74cb6054ca2e.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/8593-8d4da2b7f0b7f988.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/9396-506ae2f8cc402316.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/9775-245edb139aa5b19d.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/9909-8e8ff2db4f50602c.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/_not-found/page-cbbfb2f7911bfaae.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/admin/demands/page-c55c9c34b8d087a5.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/admin/feedbacks/page-666465365190893a.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/admin/users/page-f3bdcfeb9e382b4c.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/activities/%5Bid%5D/route-ae74a10472ea17f7.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/activities/route-51de7efd2edcae20.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/admin/demands/route-ea634082c768b395.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/auth/%5B...nextauth%5D/route-eec3f37ab6ed5371.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/calendars/%5Bid%5D/route-c7590375fa468356.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/calendars/route-c08dffc67c5345eb.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/clients/%5Bid%5D/route-69bd073f349bc45b.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/clients/route-c544759fde91388d.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/content-steps/route-aff3e2678a815a2e.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/contents/%5Bid%5D/archive/route-1e57ed1db5454dac.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/contents/%5Bid%5D/assign/route-5680c687f918c4d5.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/contents/%5Bid%5D/route-40ba664730ef9123.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/contents/%5Bid%5D/url-structuring/route-5955244873cc61b3.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/contents/assign-bulk/route-32dfce89f733484a.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/contents/remove-bulk/route-69470af1070b8109.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/contents/route-ed71248601f5e781.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/contents/update-positions/route-4cd0a14644e52103.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/db-info/route-e4e05c925e3291a2.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/debug-drive/route-928cd1462ff62924.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/demands/pending-count/route-d7e62417f3a4f455.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/drive-proxy/route-5d91415ff0255f01.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/events/%5Bid%5D/route-f3a82efe2aec7001.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/events/route-81743065a9159f33.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/feed-structuring-tasks/route-f902ecfd2e38461b.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/feedback/%5Bid%5D/route-eff67e020fcad7dc.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/feedback/route-0249e778a6d09c8f.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/general-demands/%5Bid%5D/archive/route-5fdfb2e41db260cc.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/general-demands/%5Bid%5D/route-37a8fbccf28b299a.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/general-demands/%5Bid%5D/status/route-5f7d361b0978ed59.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/general-demands/%5Bid%5D/url-structuring/route-8463ea0ffc85b22c.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/general-demands/assign-bulk/route-d8b26b236cd247f5.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/general-demands/remove-bulk/route-3335339cacbfccde.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/general-demands/route-36fd2c20be265605.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/general-demands/update-positions/route-0abb11ec4e7d8f6a.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/generate-feed-pdf/route-5a8b4e3c3297dea8.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/holidays/%5Bid%5D/route-23576cfcdbacc2b4.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/holidays/route-451c884e7eb05e40.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/monthly-planning/%5Bid%5D/show-activities/route-8901a7aa85b21491.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/monthly-planning/%5Bid%5D/status/route-8135ce2c8bb31b36.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/notifications/route-dd1154fead174f7b.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/plannings/route-63cbf4cada8c32f1.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/requirements/%5Bid%5D/route-6a586adc91282a7f.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/results-report/%5Bid%5D/route-219b8e81475cfcb6.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/results-report/route-f2c0ff1798b86782.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/signup/route-f44fff6ffa3e1ebd.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/user-activity/route-02f891c24bad632d.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/user/demands/route-16df33c509f7b574.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/users/%5Bemail%5D/password/route-96eec89525f7dc4a.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/users/%5Bemail%5D/route-f545e39c92c99dda.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/users/route-0640ed0213dbde54.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/api/weekly-activities/route-74f5e9fceaa0c3e2.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/clients/%5Bid%5D/calendars/%5BcalendarId%5D/page-0a21e74e84af998e.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/clients/%5Bid%5D/calendars/page-18da6009eaccfea7.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/clients/%5Bid%5D/page-6df8d773c778616f.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/clients/page-be101013b728b99f.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/create-client/page-ac34a7c09f08b89b.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/dashboard/page-3169bdeb8c2cc1ef.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/debug-drive/page-70699bd3d4f59d6c.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/deliveries-pdf/page-b7d72c4971eda1e5.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/deliveries/%5Bid%5D/page-edf428fb845f0bf9.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/feed-structure-pdf/page-23d652394fa1a18e.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/feed-structuring/%5Bid%5D/page-e1f0c629a3aa12aa.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/layout-3f6432c719331e1d.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/login/page-0543e2db9e29504a.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/maintenance/page-5e5b00f4609454ea.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/monthly-planning/%5Bid%5D/page-a7640207dc742ef6.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/my-demands/page-56538ae332986d08.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/notifications/page-018f6c9e7ecf21b3.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/page-e9821de06a405065.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/panel/page-bfb0d60cf56510d0.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/profile/page-cef6b6479872906f.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/requirements/%5Bid%5D/page-3e7f6bdccf6fb4da.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/results-report/%5Bid%5D/page-af9b8b173a797277.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/settings/page-4b7b06addc8f4fbf.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/signup/page-3c0c37a2ada20b4e.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/system/page-1b25e0a220dbd567.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/updates/page-d1e3a64f22a850b3.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/app/user-activity/page-376bf7db682dc929.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/badf541d.ad6fca3fd8ad4fdd.js",revision:"ad6fca3fd8ad4fdd"},{url:"/_next/static/chunks/c132bf7d-b68cdcefe23998d1.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/framework-f571e00e48325e1b.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/main-535c83e0a4dc7b08.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/main-app-ceca5f6d78663e69.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/pages/_app-577ec3feef5c1ede.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/pages/_error-3016d6dba3e884e2.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-dcf846080d14b06e.js",revision:"RNeGBXunsppNvWa8bUg7S"},{url:"/_next/static/css/75e733bf8449538b.css",revision:"75e733bf8449538b"},{url:"/_next/static/css/fc58c39f8668387c.css",revision:"fc58c39f8668387c"},{url:"/_next/static/media/569ce4b8f30dc480-s.p.woff2",revision:"ef6cefb32024deac234e82f932a95cbd"},{url:"/_next/static/media/747892c23ea88013-s.woff2",revision:"a0761690ccf4441ace5cec893b82d4ab"},{url:"/_next/static/media/8d697b304b401681-s.woff2",revision:"cc728f6c0adb04da0dfcb0fc436a8ae5"},{url:"/_next/static/media/93f479601ee12b01-s.p.woff2",revision:"da83d5f06d825c5ae65b7cca706cb312"},{url:"/_next/static/media/9610d9e46709d722-s.woff2",revision:"7b7c0ef93df188a852344fc272fc096b"},{url:"/_next/static/media/ba015fad6dcf6784-s.woff2",revision:"8ea4f719af3312a055caf09f34c89a77"},{url:"/fallback.js",revision:"44318e3fd470d763ca7afaf5eb588b07"},{url:"/google7123025.svg",revision:"45aebcf899b73c299419c150cf8a4edb"},{url:"/icon-b4desk-loading.svg",revision:"ddb4c4f935cdd757c44444f7ca8bb09b"},{url:"/icon-b4desk.png",revision:"1a1d1ad2b9820dc57d166675f050e1cf"},{url:"/icon-b4desk.svg",revision:"b5f8e116d2db804a0ed2cdab0ecb40c3"},{url:"/images/placeholder.jpg",revision:"fbe3daff49457bea9727707ca42db9bb"},{url:"/instagram.svg",revision:"eb6f803964980f3c32ebac6863f4d3b0"},{url:"/logo-b4desk-dark.png",revision:"ce42c96d0250139bbda17f09b1b9847b"},{url:"/logo-b4desk-light.png",revision:"dee7f6b06c30a7797d5aa18d0db4a603"},{url:"/logo-rodapé-17-b4(1).png",revision:"38cec9d3a43775ecd9d633afd0df0fc0"},{url:"/manifest.json",revision:"b93fd4fe3da975b8b4efa7d2118c1311"},{url:"/offline.html",revision:"5fc312cb930e99143e8c6835ebba0582"},{url:"/play.png",revision:"7d0bb85ce06e85f6875ad364e5f3536d"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:a,state:n})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s},{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")}),new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")}),new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET"),e.registerRoute((({url:e})=>!(self.origin===e.origin)),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600}),{handlerDidError:async({request:e})=>self.fallback(e)}]}),"GET")}));
